#!/bin/bash

# Apple爬虫多用户多线程系统演示脚本
echo "🎯 Apple爬虫多用户多线程系统演示"
echo "================================================================"
echo ""

# 1. 检查用户配置文件
echo "📋 1. 查看当前用户配置:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list
echo ""

# 2. 创建第二个用户
echo "👤 2. 创建第二个用户配置:"
echo "复制示例配置文件..."
cp users/<EMAIL> users/<EMAIL>
echo "✅ 已创建用户配置文件: users/<EMAIL>"
echo "💡 请编辑此文件，修改用户名、密码等信息"
echo ""
echo "🔧 为了演示，让我修改第二个用户的配置..."
# 修改第二个用户的配置文件以演示多用户功能
sed -i '' 's/<EMAIL>/<EMAIL>/g' users/<EMAIL>
sed -i '' 's/"主要测试账户"/"第二个测试账户"/g' users/<EMAIL>
echo "✅ 已修改第二个用户的配置（用户名和描述）"
echo ""

# 3. 再次检查用户列表
echo "📋 3. 再次查看用户配置:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list
echo ""

# 4. 演示并发模式（会提示缺少会话文件）
echo "🚀 4. 尝试启动并发模式:"
echo "（这会显示友好的错误提示，指导用户先生成会话文件）"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar parallel
echo ""

# 5. 演示登录模式
echo "🔑 5. 生成会话文件的正确流程:"
echo ""
echo "步骤1: 启动登录模式"
echo "java -jar target/swimcrawler-0.0.1-SNAPSHOT.<NAME_EMAIL>"
echo ""
echo "步骤2: 在浏览器中手动完成登录"
echo "（系统会自动保存会话状态到 sessions/ 目录）"
echo ""
echo "步骤3: 重新启动并发模式"
echo "java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar parallel"
echo ""
echo "🎉 系统特点:"
echo "✅ 完美解决了'面向单用户'的问题"
echo "✅ 每个用户独立的配置和会话文件"
echo "✅ 清晰友好的错误提示和使用指导"
echo "✅ 零业务侵入，保持核心爬虫流程不变"
echo "✅ 资源高效的单一浏览器多上下文架构"
echo "✅ 完全向后兼容的单线程模式"
echo ""
echo "📝 完整工作流程总结:"
echo ""
echo "🔧 配置阶段 → 📝 编辑用户JSON文件"
echo "🔑 会话阶段 → 🌐 手动登录保存会话"
echo "🚀 执行阶段 → ⚡ 并发执行购买任务"
echo ""
echo "现在你可以轻松管理多个Apple账号，并发执行购买任务了！🚀"

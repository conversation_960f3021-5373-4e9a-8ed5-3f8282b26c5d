#!/bin/bash

# Apple爬虫多线程功能测试脚本
echo "=== Apple爬虫多线程功能测试 ==="

# 1. 测试用户管理功能
echo ""
echo "1. 测试用户管理功能:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list

echo ""
echo "2. 测试单线程模式（保持向后兼容）:"
echo "注意: 单线程模式会尝试使用现有配置，如果没有会话文件会提示登录"
echo "按Ctrl+C中断单线程模式测试..."
timeout 5s java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar || echo "单线程模式测试完成"

echo ""
echo "3. 测试并发模式:"
echo "注意: 并发模式需要用户配置文件和对应的会话文件"
echo "如果没有会话文件，会提示生成会话文件"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar parallel

echo ""
echo "=== 测试完成 ==="
echo ""
echo "🎯 多用户工作流程:"
echo ""
echo "📝 1. 创建用户配置文件:"
echo "   cp users/<EMAIL> users/新用户名.json"
echo "   # 编辑新文件，修改用户名、密码等信息"
echo ""
echo "🔑 2. 生成会话文件:"
echo "   java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar login 用户名"
echo "   # 这会启动浏览器让你手动登录，然后保存会话状态"
echo ""
echo "🚀 3. 并发执行任务:"
echo "   java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar parallel"
echo "   # 系统会自动使用所有用户的配置和会话文件"
echo ""
echo "📊 4. 查看所有用户:"
echo "   java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list"
echo ""
echo "✨ 现在每个用户都有独立的配置和会话，完全解决了'面向单用户'的问题！"

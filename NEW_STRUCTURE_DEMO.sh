#!/bin/bash

# 新用户目录结构演示脚本
echo "🎯 新用户目录结构演示"
echo "================================================================"
echo ""

# 1. 显示新的目录结构
echo "📁 1. 新的目录结构:"
echo ""
tree users/ 2>/dev/null || (
    echo "users/"
    for user_dir in users/*/; do
        if [ -d "$user_dir" ]; then
            echo "├── $(basename "$user_dir")/"
            if [ -f "${user_dir}config.json" ]; then
                echo "│   ├── config.json"
            fi
            if [ -f "${user_dir}session.json" ]; then
                echo "│   └── session.json"
            else
                echo "│   └── session.json (缺失)"
            fi
        fi
    done
)
echo ""

# 2. 查看所有用户
echo "👥 2. 查看所有用户:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list
echo ""

# 3. 检查会话文件状态
echo "🔑 3. 检查会话文件状态:"
echo ""
for user_dir in users/*/; do
    if [ -d "$user_dir" ]; then
        username=$(basename "$user_dir")
        echo -n "  $username: "
        if [ -f "${user_dir}session.json" ]; then
            echo "✅ 有会话文件"
        else
            echo "❌ 缺少会话文件"
        fi
    fi
done
echo ""

# 4. 演示用户管理
echo "🔧 4. 用户管理演示:"
echo ""

echo "📝 创建新用户 <EMAIL>:"
# 创建用户目录
mkdir -p "users/<EMAIL>"

# 复制配置模板
cp "users/<EMAIL>/config.json" "users/<EMAIL>/config.json"

# 修改配置文件中的用户名和描述
sed -i '' 's/<EMAIL>/<EMAIL>/g' "users/<EMAIL>/config.json"
sed -i '' 's/"主要测试账户"/"新创建的测试用户"/g' "users/<EMAIL>/config.json"

echo "  ✅ 已创建用户目录和配置文件"
echo ""

echo "👥 再次查看所有用户:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list
echo ""

# 5. 显示文件管理的便利性
echo "📋 5. 文件管理便利性演示:"
echo ""

echo "🔍 查看特定用户的所有文件:"
echo "  ls -la users/<EMAIL>/"
ls -la "users/<EMAIL>/"
echo ""

echo "📖 查看用户配置:"
echo "  cat users/<EMAIL>/config.json | head -10"
cat "users/<EMAIL>/config.json" | head -10
echo "  ..."
echo ""

echo "🗑️ 删除用户 (演示):"
echo "  rm -rf users/<EMAIL>/"
rm -rf "users/<EMAIL>/"
echo "  ✅ 用户及其所有文件已删除"
echo ""

# 6. 最终状态
echo "📊 6. 最终用户列表:"
java -jar target/swimcrawler-0.0.1-SNAPSHOT.jar users list
echo ""

# 7. 总结新结构的优势
echo "🎉 新结构优势总结:"
echo ""
echo "✅ 更清晰的组织结构：每个用户独立目录"
echo "✅ 更容易管理：一个目录包含用户的所有文件"
echo "✅ 更好的隔离：用户数据完全分离"
echo "✅ 更简单的操作："
echo "   - 添加用户: mkdir users/新用户名 && cp 模板 users/新用户名/config.json"
echo "   - 删除用户: rm -rf users/用户名/"
echo "   - 备份用户: cp -r users/用户名 backup/"
echo "   - 查看用户: ls users/用户名/"
echo ""
echo "🚀 现在用户管理变得更加直观和高效！"

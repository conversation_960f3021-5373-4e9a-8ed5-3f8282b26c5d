./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"
./mvnw spring-boot:run -Dspring-boot.run.arguments="login <EMAIL>"

java -jar target/applecrawler-0.0.1-SNAPSHOT.jar server

curl -X POST http://localhost:8080/api/queue/start       

curl -X POST "http://localhost:8080/api/queue/send/login/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/watch-cart/<EMAIL>"

curl -X POST "http://localhost:8080/api/queue/send/purchase/<EMAIL>"

// 等待页面导航完成，使用更稳定的等待策略
            try {
                // 等待页面开始导航
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                // 额外等待确保页面完全加载
                page.waitForTimeout(2000 + (long) (Math.random() * 1000)); // 2-3秒随机延迟

                logger.info("检查页面是否跳转到登录页面...");
                // 检查是否跳转到了登录页面
                if (isLoginPage(page)) {
                    logger.info("检测到登录页面，开始登录流程...");
                    login(page);
                    checkout(page);
                } else {
                    logger.info("未检测到登录页面，继续结账流程...");
                    checkout(page);
                }
            } catch (Exception e) {
                logger.warn("检查登录页面时出错: {}", e.getMessage());
                // 如果检查失败，尝试直接进行结账流程
                logger.info("尝试直接进行结账流程...");
                checkout(page);
            }
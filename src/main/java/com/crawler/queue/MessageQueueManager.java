package com.crawler.queue;

import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.crawler.worker.CrawlerWorker;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息队列管理器
 * 负责管理消息队列、工作线程和消息分发
 */
public class MessageQueueManager {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageQueueManager.class);
    
    /** 消息队列 */
    private final BlockingQueue<CrawlerMessage> messageQueue;
    
    /** 工作线程列表 */
    private final List<CrawlerWorker> workers;
    
    /** 工作线程执行器 */
    private final List<Future<?>> workerFutures;
    
    /** 线程池 */
    private final ExecutorService executorService;
    
    /** 定时任务调度器 */
    private final ScheduledExecutorService scheduledExecutorService;
    
    /** 是否已启动 */
    private final AtomicBoolean started = new AtomicBoolean(false);
    
    /** 是否正在关闭 */
    private final AtomicBoolean shutdownRequested = new AtomicBoolean(false);
    
    /** 用户配置管理器 */
    private final UserProfileManager userProfileManager;
    
    /** 是否无头模式 */
    private final boolean headless;
    
    /** 工作线程数量 */
    private final int workerCount;

    public MessageQueueManager(int workerCount, boolean headless, UserProfileManager userProfileManager) {
        this(workerCount, headless, userProfileManager, new PriorityBlockingQueue<>(100, 
            (m1, m2) -> Integer.compare(m1.getPriority(), m2.getPriority())));
    }

    public MessageQueueManager(int workerCount, boolean headless, UserProfileManager userProfileManager, 
                              BlockingQueue<CrawlerMessage> customQueue) {
        this.workerCount = workerCount;
        this.headless = headless;
        this.userProfileManager = userProfileManager;
        this.messageQueue = customQueue;
        this.workers = new ArrayList<>();
        this.workerFutures = new ArrayList<>();
        this.executorService = Executors.newFixedThreadPool(workerCount, new ThreadFactory() {
            private final AtomicLong threadCounter = new AtomicLong(0);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("CrawlerWorker-" + threadCounter.incrementAndGet());
                thread.setDaemon(false); // 不设置为守护线程，确保能正常关闭
                return thread;
            }
        });
        
        // 初始化定时任务调度器
        this.scheduledExecutorService = Executors.newScheduledThreadPool(5, new ThreadFactory() {
            private final AtomicLong threadCounter = new AtomicLong(0);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("ScheduledTask-" + threadCounter.incrementAndGet());
                thread.setDaemon(true); // 设置为守护线程
                return thread;
            }
        });
        
        logger.info("消息队列管理器初始化 - 工作线程数: {}, 无头模式: {}", workerCount, headless);
    }

    /**
     * 启动消息队列管理器
     */
    public synchronized void start() {
        if (started.get()) {
            logger.warn("消息队列管理器已经启动");
            return;
        }
        
        logger.info("启动消息队列管理器...");
        
        // 为每个启用的用户创建专属工作线程
        List<UserProfileManager.UserProfile> enabledUsers = userProfileManager.getAllEnabledUserProfiles();
        
        if (enabledUsers.isEmpty()) {
            logger.warn("没有找到启用的用户，将不会创建任何工作线程");
        } else {
            logger.info("发现 {} 个启用的用户，为每个用户创建专属线程", enabledUsers.size());
            
            for (UserProfileManager.UserProfile userProfile : enabledUsers) {
                String username = userProfile.getUsername();
                
                CrawlerWorker worker = new CrawlerWorker(username, messageQueue, userProfileManager, headless);
                workers.add(worker);
                
                Future<?> future = executorService.submit(worker);
                workerFutures.add(future);
                
                logger.info("用户专属线程 {} 已启动", username);
                
                // 如果用户配置了定时任务，启动定时调度
                startUserScheduledTasks(userProfile);
            }
        }
        
        started.set(true);
        logger.info("消息队列管理器启动完成，{} 个用户线程正在运行", enabledUsers.size());
    }

    /**
     * 为用户启动定时任务
     */
    private void startUserScheduledTasks(UserProfileManager.UserProfile userProfile) {
        String username = userProfile.getUsername();
        UserProfileManager.TaskConfiguration tasks = userProfile.getTasks();
        
        if (tasks == null) {
            logger.debug("用户 {} 没有配置任务", username);
            return;
        }
        
        // 启动库存监控任务
        UserProfileManager.TaskConfig stockTask = tasks.getStockMonitoring();
        if (stockTask != null && stockTask.isEnabled() && stockTask.getIntervalMinutes() != null) {
            int intervalMinutes = stockTask.getIntervalMinutes();
            logger.info("为用户 {} 启动库存监控定时任务，间隔 {} 分钟", username, intervalMinutes);
            
            scheduledExecutorService.scheduleWithFixedDelay(() -> {
                try {
                    CrawlerMessage message = CrawlerMessage.createCheckStockMessage(
                        username, userProfile.getUserConfig(), userProfile.getIphoneConfig());
                    submitMessage(message);
                    logger.debug("已为用户 {} 提交库存检查定时任务", username);
                } catch (Exception e) {
                    logger.error("用户 {} 库存监控定时任务执行失败: {}", username, e.getMessage(), e);
                }
            }, 0, intervalMinutes, TimeUnit.MINUTES);
        }
        
        // 启动会话刷新任务
        UserProfileManager.TaskConfig sessionTask = tasks.getSessionRefresh();
        if (sessionTask != null && sessionTask.isEnabled() && sessionTask.getIntervalHours() != null) {
            int intervalHours = sessionTask.getIntervalHours();
            logger.info("为用户 {} 启动会话刷新定时任务，间隔 {} 小时", username, intervalHours);
            
            scheduledExecutorService.scheduleWithFixedDelay(() -> {
                try {
                    CrawlerMessage message = CrawlerMessage.createLoginMessage(
                        username, userProfile.getUserConfig(), userProfile.getIphoneConfig());
                    submitMessage(message);
                    logger.debug("已为用户 {} 提交会话刷新定时任务", username);
                } catch (Exception e) {
                    logger.error("用户 {} 会话刷新定时任务执行失败: {}", username, e.getMessage(), e);
                }
            }, intervalHours, intervalHours, TimeUnit.HOURS); // 延迟启动，避免和初始登录冲突
        }
    }

    /**
     * 提交消息到队列
     */
    public boolean submitMessage(CrawlerMessage message) {
        if (!started.get()) {
            logger.error("消息队列管理器尚未启动，无法提交消息");
            return false;
        }
        
        if (shutdownRequested.get()) {
            logger.warn("消息队列管理器正在关闭，拒绝新消息");
            return false;
        }
        
        try {
            boolean offered = messageQueue.offer(message);
            if (offered) {
                logger.info("消息已提交到队列: {} (类型: {}, 目标: {})", 
                    message.getMessageId(), message.getMessageType(), 
                    message.getTargetType() == CrawlerMessage.TargetType.SPECIFIC_USER ? 
                        message.getTargetUsername() : message.getTargetType());
            } else {
                logger.warn("消息队列已满，无法提交消息: {}", message.getMessageId());
            }
            return offered;
        } catch (Exception e) {
            logger.error("提交消息到队列失败: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 批量提交消息到队列
     */
    public int submitMessages(List<CrawlerMessage> messages) {
        int successCount = 0;
        for (CrawlerMessage message : messages) {
            if (submitMessage(message)) {
                successCount++;
            }
        }
        logger.info("批量提交消息完成，成功: {}/{}", successCount, messages.size());
        return successCount;
    }

    /**
     * 向特定用户发送消息
     */
    public boolean sendMessageToUser(String username, CrawlerMessage.MessageType messageType, 
                                   int priority) {
        try {
            // 获取用户配置
            var profile = userProfileManager.loadUserProfile(username);
            if (profile == null) {
                logger.error("用户 {} 不存在，无法发送消息", username);
                return false;
            }
            
            CrawlerMessage message = new CrawlerMessage.Builder()
                    .messageType(messageType)
                    .targetSpecificUser(username)
                    .userConfig(profile.getUserConfig())
                    .iphoneConfig(profile.getIphoneConfig())
                    .watchConfig(profile.getWatchConfig())
                    .priority(priority)
                    .build();
            
            return submitMessage(message);
        } catch (Exception e) {
            logger.error("向用户 {} 发送消息失败: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 向所有用户发送消息
     */
    public int sendMessageToAllUsers(CrawlerMessage.MessageType messageType, int priority) {
        try {
            var userProfiles = userProfileManager.listUserProfiles();
            List<CrawlerMessage> messages = new ArrayList<>();
            
            for (var profile : userProfiles) {
                CrawlerMessage message = new CrawlerMessage.Builder()
                        .messageType(messageType)
                        .targetSpecificUser(profile.getUsername())
                        .userConfig(profile.getUserConfig())
                        .iphoneConfig(profile.getIphoneConfig())
                        .watchConfig(profile.getWatchConfig())
                        .priority(priority)
                        .build();
                messages.add(message);
            }
            
            int successCount = submitMessages(messages);
            logger.info("向所有用户发送消息完成，成功: {}/{}", successCount, userProfiles.size());
            return successCount;
        } catch (Exception e) {
            logger.error("向所有用户发送消息失败: {}", e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 发送健康检查消息
     */
    public int sendHealthCheckToAllWorkers() {
        List<CrawlerMessage> messages = new ArrayList<>();
        for (int i = 0; i < workerCount; i++) {
            CrawlerMessage message = new CrawlerMessage.Builder()
                    .messageType(CrawlerMessage.MessageType.HEALTH_CHECK)
                    .targetSystem()
                    .priority(10) // 较低优先级
                    .build();
            messages.add(message);
        }
        return submitMessages(messages);
    }

    /**
     * 获取队列状态
     */
    public QueueStatus getQueueStatus() {
        return new QueueStatus(
                messageQueue.size(),
                workers.size(),
                getActiveWorkerCount(),
                getTotalProcessedMessages(),
                getTotalSuccessMessages(),
                getTotalFailedMessages()
        );
    }

    /**
     * 获取所有工作线程状态
     */
    public List<CrawlerWorker.WorkerStats> getWorkerStats() {
        List<CrawlerWorker.WorkerStats> stats = new ArrayList<>();
        for (CrawlerWorker worker : workers) {
            stats.add(worker.getStats());
        }
        return stats;
    }

    /**
     * 获取活跃工作线程数量
     */
    private int getActiveWorkerCount() {
        return (int) workers.stream()
                .filter(worker -> worker.getStatus() == CrawlerWorker.WorkerStatus.RUNNING || 
                                worker.getStatus() == CrawlerWorker.WorkerStatus.PROCESSING)
                .count();
    }

    /**
     * 获取总处理消息数
     */
    private long getTotalProcessedMessages() {
        return workers.stream()
                .mapToLong(CrawlerWorker::getProcessedMessageCount)
                .sum();
    }

    /**
     * 获取总成功消息数
     */
    private long getTotalSuccessMessages() {
        return workers.stream()
                .mapToLong(CrawlerWorker::getSuccessMessageCount)
                .sum();
    }

    /**
     * 获取总失败消息数
     */
    private long getTotalFailedMessages() {
        return workers.stream()
                .mapToLong(CrawlerWorker::getFailedMessageCount)
                .sum();
    }

    /**
     * 优雅关闭消息队列管理器
     */
    public synchronized void shutdown() {
        if (!started.get() || shutdownRequested.get()) {
            logger.warn("消息队列管理器未启动或已在关闭中");
            return;
        }
        
        logger.info("开始关闭消息队列管理器...");
        shutdownRequested.set(true);
        
        // 发送停止消息给所有工作线程
        for (int i = 0; i < workers.size(); i++) {
            CrawlerMessage stopMessage = CrawlerMessage.createStopWorkerMessage();
            messageQueue.offer(stopMessage);
        }
        
        // 等待工作线程完成
        for (int i = 0; i < workerFutures.size(); i++) {
            Future<?> future = workerFutures.get(i);
            CrawlerWorker worker = workers.get(i);
            try {
                logger.info("等待工作线程 {} 完成...", worker.getWorkerId());
                future.get(30, TimeUnit.SECONDS); // 等待30秒
            } catch (TimeoutException e) {
                logger.warn("工作线程 {} 在30秒内未完成，强制停止", worker.getWorkerId());
                future.cancel(true);
                worker.forceShutdown();
            } catch (Exception e) {
                logger.warn("等待工作线程 {} 完成时发生异常: {}", worker.getWorkerId(), e.getMessage());
            }
        }
        
        // 关闭线程池
        executorService.shutdown();
        try {
            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                logger.warn("线程池在10秒内未完成关闭，强制关闭");
                executorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            logger.warn("等待线程池关闭时被中断");
            executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        // 关闭定时任务调度器
        scheduledExecutorService.shutdown();
        try {
            if (!scheduledExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                logger.warn("定时任务调度器在5秒内未完成关闭，强制关闭");
                scheduledExecutorService.shutdownNow();
            }
        } catch (InterruptedException e) {
            logger.warn("等待定时任务调度器关闭时被中断");
            scheduledExecutorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        started.set(false);
        logger.info("消息队列管理器已关闭");
    }

    /**
     * 强制关闭
     */
    public synchronized void forceShutdown() {
        logger.warn("强制关闭消息队列管理器");
        
        // 停止所有工作线程
        for (CrawlerWorker worker : workers) {
            worker.forceShutdown();
        }
        
        // 取消所有任务
        for (Future<?> future : workerFutures) {
            future.cancel(true);
        }
        
        // 立即关闭线程池
        executorService.shutdownNow();
        
        // 立即关闭定时任务调度器
        scheduledExecutorService.shutdownNow();
        
        started.set(false);
        shutdownRequested.set(true);
    }

    public boolean isStarted() {
        return started.get();
    }

    public boolean isShuttingDown() {
        return shutdownRequested.get();
    }

    public int getQueueSize() {
        return messageQueue.size();
    }

    public int getWorkerCount() {
        return workers.size();
    }

    /**
     * 队列状态信息
     */
    public static class QueueStatus {
        private final int queueSize;
        private final int totalWorkers;
        private final int activeWorkers;
        private final long totalProcessedMessages;
        private final long totalSuccessMessages;
        private final long totalFailedMessages;

        public QueueStatus(int queueSize, int totalWorkers, int activeWorkers, 
                          long totalProcessedMessages, long totalSuccessMessages, long totalFailedMessages) {
            this.queueSize = queueSize;
            this.totalWorkers = totalWorkers;
            this.activeWorkers = activeWorkers;
            this.totalProcessedMessages = totalProcessedMessages;
            this.totalSuccessMessages = totalSuccessMessages;
            this.totalFailedMessages = totalFailedMessages;
        }

        // Getters
        public int getQueueSize() { return queueSize; }
        public int getTotalWorkers() { return totalWorkers; }
        public int getActiveWorkers() { return activeWorkers; }
        public long getTotalProcessedMessages() { return totalProcessedMessages; }
        public long getTotalSuccessMessages() { return totalSuccessMessages; }
        public long getTotalFailedMessages() { return totalFailedMessages; }

        @Override
        public String toString() {
            return "QueueStatus{" +
                    "queueSize=" + queueSize +
                    ", totalWorkers=" + totalWorkers +
                    ", activeWorkers=" + activeWorkers +
                    ", totalProcessed=" + totalProcessedMessages +
                    ", totalSuccess=" + totalSuccessMessages +
                    ", totalFailed=" + totalFailedMessages +
                    '}';
        }
    }
}

package com.crawler.user;

import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.config.AppleWatchConfig;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * 用户配置文件管理器
 * 负责管理用户配置文件，包含用户信息和iPhone偏好
 */
@Component
public class UserProfileManager {

    private static final Logger logger = LoggerFactory.getLogger(UserProfileManager.class);
    private static final Path USERS_DIR = Paths.get("users");
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final String CONFIG_FILE_NAME = "config.json";
    private static final String SESSION_FILE_NAME = "apple-auth.json";

    static {
        objectMapper.registerModule(new JavaTimeModule());
    }

    /**
     * 用户配置文件类
     */
    public static class UserProfile {
        private String username;
        private String password;
        private AppleUserConfig userConfig;
        private AppleIphoneConfig iphoneConfig;
        private AppleWatchConfig watchConfig;
        private String sessionFileName;
        private String description;
        private boolean enabled;
        private TaskConfiguration tasks;

        // 默认构造函数
        public UserProfile() {}

        // Getters and Setters
        public String getUsername() { return username; }
        public void setUsername(String username) { this.username = username; }

        public String getPassword() { return password; }
        public void setPassword(String password) { this.password = password; }

        public AppleUserConfig getUserConfig() { return userConfig; }
        public void setUserConfig(AppleUserConfig userConfig) { this.userConfig = userConfig; }

        public AppleIphoneConfig getIphoneConfig() { return iphoneConfig; }
        public void setIphoneConfig(AppleIphoneConfig iphoneConfig) { this.iphoneConfig = iphoneConfig; }

        public AppleWatchConfig getWatchConfig() { return watchConfig; }
        public void setWatchConfig(AppleWatchConfig watchConfig) { this.watchConfig = watchConfig; }

        public String getSessionFileName() { return sessionFileName; }
        public void setSessionFileName(String sessionFileName) { this.sessionFileName = sessionFileName; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public TaskConfiguration getTasks() { return tasks; }
        public void setTasks(TaskConfiguration tasks) { this.tasks = tasks; }

        @Override
        public String toString() {
            return String.format("UserProfile{username='%s', description='%s', enabled=%s}",
                username, description != null ? description : "无描述", enabled);
        }
    }

    /**
     * 任务配置类
     */
    public static class TaskConfiguration {
        private TaskConfig stockMonitoring;
        private TaskConfig sessionRefresh;
        private TaskConfig purchaseFlow;

        public TaskConfig getStockMonitoring() { return stockMonitoring; }
        public void setStockMonitoring(TaskConfig stockMonitoring) { this.stockMonitoring = stockMonitoring; }

        public TaskConfig getSessionRefresh() { return sessionRefresh; }
        public void setSessionRefresh(TaskConfig sessionRefresh) { this.sessionRefresh = sessionRefresh; }

        public TaskConfig getPurchaseFlow() { return purchaseFlow; }
        public void setPurchaseFlow(TaskConfig purchaseFlow) { this.purchaseFlow = purchaseFlow; }
    }

    /**
     * 单个任务配置类
     */
    public static class TaskConfig {
        private boolean enabled;
        private String description;
        private Integer intervalMinutes;
        private Integer intervalHours;

        public boolean isEnabled() { return enabled; }
        public void setEnabled(boolean enabled) { this.enabled = enabled; }

        public String getDescription() { return description; }
        public void setDescription(String description) { this.description = description; }

        public Integer getIntervalMinutes() { return intervalMinutes; }
        public void setIntervalMinutes(Integer intervalMinutes) { this.intervalMinutes = intervalMinutes; }

        public Integer getIntervalHours() { return intervalHours; }
        public void setIntervalHours(Integer intervalHours) { this.intervalHours = intervalHours; }
    }

    /**
     * 初始化用户目录
     */
    public void initialize() {
        try {
            if (!Files.exists(USERS_DIR)) {
                Files.createDirectories(USERS_DIR);
                logger.info("创建用户配置文件目录: {}", USERS_DIR.toAbsolutePath());
            }
        } catch (Exception e) {
            logger.error("创建用户目录失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 列出所有用户配置文件
     */
    public List<UserProfile> listUserProfiles() {
        List<UserProfile> profiles = new ArrayList<>();

        try {
            if (!Files.exists(USERS_DIR)) {
                logger.warn("用户配置文件目录不存在: {}", USERS_DIR);
                return profiles;
            }

            try (Stream<Path> userDirs = Files.list(USERS_DIR)) {
                userDirs.filter(Files::isDirectory)
                       .forEach(userDir -> {
                           try {
                               String username = userDir.getFileName().toString();
                               UserProfile profile = loadUserProfile(username);
                               if (profile != null) {
                                   profiles.add(profile);
                               }
                           } catch (Exception e) {
                               logger.error("加载用户配置文件失败: {} - {}", userDir, e.getMessage(), e);
                           }
                       });
            }
        } catch (Exception e) {
            logger.error("列出用户配置文件失败: {}", e.getMessage(), e);
        }

        return profiles;
    }

    /**
     * 加载用户配置文件
     */
    public UserProfile loadUserProfile(String username) {
        Path userDir = USERS_DIR.resolve(username);
        Path configFile = userDir.resolve(CONFIG_FILE_NAME);

        try {
            if (!Files.exists(configFile)) {
                logger.warn("用户配置文件不存在: {}", configFile);
                return null;
            }

            String content = Files.readString(configFile);
            JsonNode jsonNode = objectMapper.readTree(content);

            // 解析用户信息
            AppleUserConfig userConfig = parseUserConfig(jsonNode);
            AppleIphoneConfig iphoneConfig = parseIphoneConfig(jsonNode);
            AppleWatchConfig watchConfig = parseWatchConfig(jsonNode);

            UserProfile profile = new UserProfile();

            // 检查必需字段
            if (!jsonNode.has("username") || jsonNode.get("username").isNull()) {
                logger.error("用户配置文件缺少必需字段: username - {}", username);
                return null;
            }
            if (!jsonNode.has("password") || jsonNode.get("password").isNull()) {
                logger.error("用户配置文件缺少必需字段: password - {}", username);
                return null;
            }

            profile.setUsername(jsonNode.get("username").asText());
            profile.setPassword(jsonNode.get("password").asText());
            profile.setUserConfig(userConfig);
            profile.setIphoneConfig(iphoneConfig);
            profile.setWatchConfig(watchConfig);

            if (jsonNode.has("sessionFileName") && !jsonNode.get("sessionFileName").isNull()) {
                profile.setSessionFileName(jsonNode.get("sessionFileName").asText());
            }

            if (jsonNode.has("description") && !jsonNode.get("description").isNull()) {
                profile.setDescription(jsonNode.get("description").asText());
            }

            // 解析enabled字段，默认为true
            if (jsonNode.has("enabled") && !jsonNode.get("enabled").isNull()) {
                profile.setEnabled(jsonNode.get("enabled").asBoolean());
            } else {
                profile.setEnabled(true); // 默认启用
            }

            // 解析任务配置
            if (jsonNode.has("tasks") && !jsonNode.get("tasks").isNull()) {
                TaskConfiguration tasks = parseTaskConfiguration(jsonNode.get("tasks"));
                profile.setTasks(tasks);
            }

            return profile;

        } catch (Exception e) {
            logger.error("加载用户配置文件失败: {} - {}", username, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 保存用户配置文件
     */
    public boolean saveUserProfile(UserProfile profile) {
        try {
            String username = profile.getUsername();
            Path userDir = USERS_DIR.resolve(username);
            Path configFile = userDir.resolve(CONFIG_FILE_NAME);

            // 确保用户目录存在
            if (!Files.exists(userDir)) {
                Files.createDirectories(userDir);
                logger.info("创建用户目录: {}", userDir.toAbsolutePath());
            }

            String jsonContent = objectMapper.writerWithDefaultPrettyPrinter()
                .writeValueAsString(profile);

            Files.writeString(configFile, jsonContent);

            logger.info("用户配置文件已保存: {}", configFile);
            return true;

        } catch (Exception e) {
            logger.error("保存用户配置文件失败: {} - {}", profile.getUsername(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 删除用户配置文件和整个用户目录
     */
    public boolean deleteUserProfile(String username) {
        Path userDir = USERS_DIR.resolve(username);

        try {
            if (Files.exists(userDir)) {
                // 递归删除整个用户目录
                Files.walk(userDir)
                     .sorted((a, b) -> b.compareTo(a)) // 先删除文件，后删除目录
                     .forEach(path -> {
                         try {
                             Files.delete(path);
                         } catch (Exception e) {
                             logger.warn("删除文件失败: {} - {}", path, e.getMessage());
                         }
                     });
                
                logger.info("用户目录已删除: {}", userDir);
                return true;
            } else {
                logger.warn("用户目录不存在: {}", userDir);
                return false;
            }
        } catch (Exception e) {
            logger.error("删除用户目录失败: {} - {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 解析任务配置
     */
    private TaskConfiguration parseTaskConfiguration(JsonNode tasksNode) {
        TaskConfiguration tasks = new TaskConfiguration();

        if (tasksNode.has("stockMonitoring")) {
            tasks.setStockMonitoring(parseTaskConfig(tasksNode.get("stockMonitoring")));
        }

        if (tasksNode.has("sessionRefresh")) {
            tasks.setSessionRefresh(parseTaskConfig(tasksNode.get("sessionRefresh")));
        }

        if (tasksNode.has("purchaseFlow")) {
            tasks.setPurchaseFlow(parseTaskConfig(tasksNode.get("purchaseFlow")));
        }

        return tasks;
    }

    /**
     * 解析单个任务配置
     */
    private TaskConfig parseTaskConfig(JsonNode taskNode) {
        TaskConfig task = new TaskConfig();

        if (taskNode.has("enabled")) {
            task.setEnabled(taskNode.get("enabled").asBoolean());
        }

        if (taskNode.has("description")) {
            task.setDescription(taskNode.get("description").asText());
        }

        if (taskNode.has("intervalMinutes")) {
            task.setIntervalMinutes(taskNode.get("intervalMinutes").asInt());
        }

        if (taskNode.has("intervalHours")) {
            task.setIntervalHours(taskNode.get("intervalHours").asInt());
        }

        return task;
    }

    /**
     * 解析用户信息配置
     */
    private AppleUserConfig parseUserConfig(JsonNode jsonNode) {
        AppleUserConfig config = new AppleUserConfig();

        if (jsonNode.has("username") && !jsonNode.get("username").isNull()) {
            config.setUsername(jsonNode.get("username").asText());
        }
        if (jsonNode.has("password") && !jsonNode.get("password").isNull()) {
            config.setPassword(jsonNode.get("password").asText());
        }

        if (jsonNode.has("userConfig") && !jsonNode.get("userConfig").isNull()) {
            JsonNode userNode = jsonNode.get("userConfig");

            if (userNode.has("firstName") && !userNode.get("firstName").isNull()) {
                config.setFirstName(userNode.get("firstName").asText());
            }
            if (userNode.has("lastName") && !userNode.get("lastName").isNull()) {
                config.setLastName(userNode.get("lastName").asText());
            }
            if (userNode.has("phone") && !userNode.get("phone").isNull()) {
                config.setPhone(userNode.get("phone").asText());
            }
            if (userNode.has("email") && !userNode.get("email").isNull()) {
                config.setEmail(userNode.get("email").asText());
            }
            if (userNode.has("address") && !userNode.get("address").isNull()) {
                config.setAddress(userNode.get("address").asText());
            }
            if (userNode.has("city") && !userNode.get("city").isNull()) {
                config.setCity(userNode.get("city").asText());
            }
            if (userNode.has("province") && !userNode.get("province").isNull()) {
                config.setProvince(userNode.get("province").asText());
            }
            if (userNode.has("postalCode") && !userNode.get("postalCode").isNull()) {
                config.setPostalCode(userNode.get("postalCode").asText());
            }
            if (userNode.has("idCardNumber") && !userNode.get("idCardNumber").isNull()) {
                config.setIdCardNumber(userNode.get("idCardNumber").asText());
            }
            if (userNode.has("idCardName") && !userNode.get("idCardName").isNull()) {
                config.setIdCardName(userNode.get("idCardName").asText());
            }
        }

        return config;
    }

    /**
     * 解析iPhone配置信息
     */
    private AppleIphoneConfig parseIphoneConfig(JsonNode jsonNode) {
        AppleIphoneConfig config = new AppleIphoneConfig();

        if (jsonNode.has("iphoneConfig") && !jsonNode.get("iphoneConfig").isNull()) {
            JsonNode iphoneNode = jsonNode.get("iphoneConfig");

            if (iphoneNode.has("buyUrl") && !iphoneNode.get("buyUrl").isNull()) {
                config.setBuyUrl(iphoneNode.get("buyUrl").asText());
            }
            if (iphoneNode.has("checkoutUrl") && !iphoneNode.get("checkoutUrl").isNull()) {
                config.setCheckoutUrl(iphoneNode.get("checkoutUrl").asText());
            }
            if (iphoneNode.has("model") && !iphoneNode.get("model").isNull()) {
                config.setModel(iphoneNode.get("model").asText());
            }
            if (iphoneNode.has("modelDisplayName") && !iphoneNode.get("modelDisplayName").isNull()) {
                config.setModelDisplayName(iphoneNode.get("modelDisplayName").asText());
            }
            if (iphoneNode.has("requiresModelSelection") && !iphoneNode.get("requiresModelSelection").isNull()) {
                config.setRequiresModelSelection(iphoneNode.get("requiresModelSelection").asBoolean());
            }
            if (iphoneNode.has("alternateColors") && iphoneNode.get("alternateColors").isArray()) {
                List<AppleIphoneConfig.ColorOption> alternateColors = new ArrayList<>();
                for (JsonNode colorNode : iphoneNode.get("alternateColors")) {
                    AppleIphoneConfig.ColorOption option = new AppleIphoneConfig.ColorOption();
                    if (colorNode.has("color") && !colorNode.get("color").isNull()) {
                        option.setColor(colorNode.get("color").asText());
                    }
                    if (colorNode.has("colorDisplayName") && !colorNode.get("colorDisplayName").isNull()) {
                        option.setColorDisplayName(colorNode.get("colorDisplayName").asText());
                    }
                    alternateColors.add(option);
                }
                config.setAlternateColors(alternateColors);
            }
            if (iphoneNode.has("color") && !iphoneNode.get("color").isNull()) {
                config.setColor(iphoneNode.get("color").asText());
            }
            if (iphoneNode.has("colorDisplayName") && !iphoneNode.get("colorDisplayName").isNull()) {
                config.setColorDisplayName(iphoneNode.get("colorDisplayName").asText());
            }
            if (iphoneNode.has("storage") && !iphoneNode.get("storage").isNull()) {
                config.setStorage(iphoneNode.get("storage").asText());
            }
            if (iphoneNode.has("storageDisplayName") && !iphoneNode.get("storageDisplayName").isNull()) {
                config.setStorageDisplayName(iphoneNode.get("storageDisplayName").asText());
            }
            if (iphoneNode.has("tradeIn") && !iphoneNode.get("tradeIn").isNull()) {
                config.setTradeIn(iphoneNode.get("tradeIn").asBoolean());
            }
            if (iphoneNode.has("appleCare") && !iphoneNode.get("appleCare").isNull()) {
                config.setAppleCare(iphoneNode.get("appleCare").asBoolean());
            }
        }

        return config;
    }

    /**
     * 解析 Apple Watch 购物配置
     */
    private AppleWatchConfig parseWatchConfig(JsonNode jsonNode) {
        if (!jsonNode.has("watchConfig") || jsonNode.get("watchConfig").isNull()) {
            return null;
        }

        JsonNode watchNode = jsonNode.get("watchConfig");
        AppleWatchConfig config = new AppleWatchConfig();

        if (watchNode.has("buyUrl") && !watchNode.get("buyUrl").isNull()) {
            config.setBuyUrl(watchNode.get("buyUrl").asText());
        }
        if (watchNode.has("checkoutUrl") && !watchNode.get("checkoutUrl").isNull()) {
            config.setCheckoutUrl(watchNode.get("checkoutUrl").asText());
        }
        if (watchNode.has("caseDisplayName") && !watchNode.get("caseDisplayName").isNull()) {
            config.setCaseDisplayName(watchNode.get("caseDisplayName").asText());
        }
        if (watchNode.has("caseValue") && !watchNode.get("caseValue").isNull()) {
            config.setCaseValue(watchNode.get("caseValue").asText());
        }
        if (watchNode.has("bandStyleDisplayName") && !watchNode.get("bandStyleDisplayName").isNull()) {
            config.setBandStyleDisplayName(watchNode.get("bandStyleDisplayName").asText());
        }
        if (watchNode.has("bandStyleValue") && !watchNode.get("bandStyleValue").isNull()) {
            config.setBandStyleValue(watchNode.get("bandStyleValue").asText());
        }
        if (watchNode.has("bandColorDisplayName") && !watchNode.get("bandColorDisplayName").isNull()) {
            config.setBandColorDisplayName(watchNode.get("bandColorDisplayName").asText());
        }
        if (watchNode.has("bandColorValue") && !watchNode.get("bandColorValue").isNull()) {
            config.setBandColorValue(watchNode.get("bandColorValue").asText());
        }
        if (watchNode.has("bandSizeDisplayName") && !watchNode.get("bandSizeDisplayName").isNull()) {
            config.setBandSizeDisplayName(watchNode.get("bandSizeDisplayName").asText());
        }
        if (watchNode.has("bandSizeValue") && !watchNode.get("bandSizeValue").isNull()) {
            config.setBandSizeValue(watchNode.get("bandSizeValue").asText());
        }
        if (watchNode.has("tradeIn") && !watchNode.get("tradeIn").isNull()) {
            config.setTradeIn(watchNode.get("tradeIn").asBoolean());
        }
        if (watchNode.has("appleCare") && !watchNode.get("appleCare").isNull()) {
            config.setAppleCare(watchNode.get("appleCare").asBoolean());
        }

        return config;
    }

    /**
     * 获取用户会话文件路径
     */
    public Path getSessionFilePath(String username) {
        return USERS_DIR.resolve(username).resolve(SESSION_FILE_NAME);
    }

    /**
     * 从用户配置文件创建会话文件名（保持兼容性）
     */
    public String getSessionFileName(String username) {
        return SESSION_FILE_NAME;
    }

    /**
     * 检查用户配置文件是否存在
     */
    public boolean userProfileExists(String username) {
        Path configFile = USERS_DIR.resolve(username).resolve(CONFIG_FILE_NAME);
        return Files.exists(configFile);
    }

    /**
     * 检查用户会话文件是否存在
     */
    public boolean userSessionExists(String username) {
        Path sessionFile = getSessionFilePath(username);
        return Files.exists(sessionFile);
    }

    /**
     * 获取所有启用的用户配置文件
     */
    public List<UserProfile> getAllEnabledUserProfiles() {
        List<UserProfile> enabledProfiles = new ArrayList<>();
        
        try {
            if (!Files.exists(USERS_DIR)) {
                logger.warn("用户目录不存在: {}", USERS_DIR);
                return enabledProfiles;
            }
            
            Files.list(USERS_DIR)
                .filter(Files::isDirectory)
                .forEach(userDir -> {
                    String username = userDir.getFileName().toString();
                    UserProfile profile = loadUserProfile(username);
                    if (profile != null && profile.isEnabled()) {
                        enabledProfiles.add(profile);
                        logger.info("加载启用的用户: {}", username);
                    } else if (profile != null && !profile.isEnabled()) {
                        logger.info("跳过禁用的用户: {}", username);
                    }
                });
                
        } catch (Exception e) {
            logger.error("获取启用用户列表失败: {}", e.getMessage(), e);
        }
        
        return enabledProfiles;
    }

    /**
     * 创建用户目录
     */
    public boolean createUserDirectory(String username) {
        try {
            Path userDir = USERS_DIR.resolve(username);
            if (!Files.exists(userDir)) {
                Files.createDirectories(userDir);
                logger.info("创建用户目录: {}", userDir.toAbsolutePath());
                return true;
            } else {
                logger.warn("用户目录已存在: {}", userDir);
                return false;
            }
        } catch (Exception e) {
            logger.error("创建用户目录失败: {} - {}", username, e.getMessage(), e);
            return false;
        }
    }
}

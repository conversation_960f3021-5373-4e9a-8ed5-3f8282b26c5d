package com.crawler.apple;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.Instant;
import java.util.Arrays;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;


import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.ElementHandle;
import com.microsoft.playwright.FrameLocator;
import com.microsoft.playwright.Locator;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.PlaywrightException;
import com.microsoft.playwright.options.AriaRole;
import com.microsoft.playwright.options.ColorScheme;
import com.microsoft.playwright.options.Geolocation;
import com.microsoft.playwright.options.LoadState;
import com.microsoft.playwright.options.WaitForSelectorState;
import com.crawler.config.AppleUserConfig;
import com.crawler.config.AppleIphoneConfig;
import com.crawler.util.ConfigValidator;

@Service
public class AppleLoginCrawler {
    private static final Logger logger = LoggerFactory.getLogger(AppleLoginCrawler.class);
    private static final String APPLE_ACCOUNT_HOME = "https://www.apple.com.cn/shop/account/home";
    private static final String APPLE_SIGN_IN_PREFIX = "https://signin.apple.com";

    // 支持多线程模式的注入页面对象
    private final Page injectedPage;

    @PostConstruct
    public void init() {
        if (userConfig != null && iphoneConfig != null) {
            logger.info("Apple爬虫配置初始化:");
            logger.info("用户配置: {}", userConfig);
            logger.info("iPhone配置: {}", iphoneConfig);
        }
    }

    @Autowired(required = false)
    private AppleUserConfig userConfig;

    @Autowired(required = false)
    private AppleIphoneConfig iphoneConfig;

    // Spring Boot 默认构造函数（单例模式）
    public AppleLoginCrawler() {
        this.injectedPage = null;
    }

    // 多线程模式构造函数（接收注入的Page对象）
    public AppleLoginCrawler(Page page, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        this.injectedPage = page;
        this.userConfig = userConfig;
        this.iphoneConfig = iphoneConfig;
    }

    @Value("${apple.login.username:<EMAIL>}")
    private String username;

    @Value("${apple.login.password:Aa112211}")
    private String password;

    @Value("${apple.login.headless:true}")
    private boolean headless;

    @Value("${apple.auth.file.path:apple-auth.json}")
    private String authFilePath;

    @Value("${apple.screenshot.dir:${user.home}/Desktop/AppleScreenshots}")
    private String screenshotDir;

    @Value("${apple.record.video:true}")
    private boolean recordVideo;

    @Value("${apple.video.dir:videos}")
    private String videoDir;

    @Value("${apple.pickup.retry.count:3}")
    private int pickupRetryCount;
    
    @Value("${apple.network.error.detection:true}")
    private boolean networkErrorDetection;
    
    @Value("${apple.use.persistent.context:false}")
    private boolean usePersistentContext;
    
    // 用于跟踪是否检测到514错误
    private volatile boolean http514ErrorDetected = false;

    /**
     * 多线程模式的主要业务流程入口
     * 使用注入的Page对象执行完整的购买流程
     */
    public String start() {
        if (injectedPage == null) {
            throw new IllegalStateException("多线程模式下必须通过构造函数注入Page对象");
        }
        
        logger.info("开始执行多线程模式的Apple购买流程...");
        
        try {
            // 直接使用注入的页面执行购买流程
            executeWithInjectedPage(injectedPage);
            return "任务处理完毕";
        } catch (Exception e) {
            logger.error("多线程模式执行失败: {}", e.getMessage(), e);
            return "任务失败: " + e.getMessage();
        }
    }

    /**
     * 使用注入的页面执行购买流程
     */
    private void executeWithInjectedPage(Page page) {
        try {
            logger.info("使用注入的页面开始购买流程...");
            
            // 添加商品到购物车
            addIphone17ProToCart(page);
            
            // 等待页面加载并检查是否需要登录
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            
            if (isCheckoutPage(page)) {
                logger.info("检测到已在结账页面，开始结账流程");
                checkout(page);
            } else {
                logger.info("需要登录，但多线程模式下应该已经通过会话文件登录");
                // 在多线程模式下，会话状态应该已经通过storageState加载
                // 如果还需要登录，说明会话可能已过期
                throw new IllegalStateException("会话可能已过期，请重新生成会话文件");
            }
            
            logger.info("购买流程执行完成");
            
        } catch (Exception e) {
            logger.error("使用注入页面执行购买流程时出错: {}", e.getMessage(), e);
            throw e;
        }
    }

    /**
     * 专门用于登录并生成会话文件的方法
     * 这个方法会强制进行登录流程，不检查现有会话
     * 注意：仍然使用 application.properties 中的 username 和 password 配置
     *
     * @param username 要保存会话文件的用户名目录
     */
    public void loginAndGenerateSession(String username) {
        logger.info("开始执行登录流程以生成会话文件...");
        logger.info("保存目标目录: users/{}/apple-auth.json", username);

        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;

        // 统一保存到 users/<username>/apple-auth.json
        Path storageFile = Paths.get("users", username, "apple-auth.json");

        try {
            playwright = Playwright.create();

            // 强制使用普通模式进行登录（不使用持久化上下文）
            logger.info("使用普通浏览器模式进行登录");
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setArgs(Arrays.asList(
                            "--disable-blink-features=AutomationControlled",
                            "--start-maximized",
                            "--disable-webrtc"))
                    .setTimeout(120000);

            browser = playwright.chromium().launch(launchOptions);
                    
            // 创建新的浏览器上下文（不加载现有会话）
            Browser.NewContextOptions contextOptions = new Browser.NewContextOptions();
            
            // 视频录制配置（方便观察登录过程）
            if (recordVideo) {
                logger.info("启用视频录制，保存目录: {}", videoDir);
                Path videoPath = Paths.get(videoDir);
                try {
                    if (!Files.exists(videoPath)) {
                        Files.createDirectories(videoPath);
                    }
                    contextOptions.setRecordVideoDir(videoPath).setRecordVideoSize(1280, 720);
                    if (headless) {
                        contextOptions.setViewportSize(1280, 720);
                    }
                } catch (Exception e) {
                    logger.warn("设置视频录制失败: {}", e.getMessage());
                }
            }
            
            // headless模式的环境参数
            if (headless) {
                logger.info("检测到 headless 模式，应用额外的真实环境参数...");
                String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                        + "AppleWebKit/537.36 (KHTML, like Gecko) "
                        + "Chrome/********* Safari/537.36";
                contextOptions
                        .setUserAgent(realUserAgent)
                        .setLocale("zh-CN")
                        .setTimezoneId("Asia/Shanghai")
                        .setColorScheme(ColorScheme.LIGHT)
                        .setExtraHTTPHeaders(Map.of(
                                "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                                "Sec-CH-UA-Platform", "\"macOS\""))
                        .setGeolocation(new Geolocation(31.2304, 121.4737));
            }

            context = browser.newContext(contextOptions);

            // 添加反检测脚本
            context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
            context.addInitScript(
                    "() => {\n  try {\n    Object.defineProperty(Navigator.prototype, 'webdriver', {\n      get: () => undefined,\n      configurable: true\n    });\n  } catch (err) {\n    console.debug('skip webdriver override', err);\n  }\n}");

            if (headless) {
                context.addInitScript(
                        "() => {\n  try {\n    Object.defineProperty(window, 'chrome', { value: { runtime: {} }, configurable: true });\n    Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5], configurable: true });\n    Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh'], configurable: true });\n    Object.defineProperty(navigator, 'platform', { get: () => 'MacIntel', configurable: true });\n    Object.defineProperty(navigator, 'cookieEnabled', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'onLine', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'userAgent', { get: () => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'vendor', { get: () => 'Google Inc.', configurable: true });\n    Object.defineProperty(navigator, 'appVersion', { get: () => '5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8, configurable: true });\n    Object.defineProperty(navigator, 'deviceMemory', { get: () => 8, configurable: true });\n    Object.defineProperty(screen, 'width', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'height', { get: () => 1080, configurable: true });\n    Object.defineProperty(screen, 'colorDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'pixelDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'availWidth', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'availHeight', { get: () => 1080, configurable: true });\n    Object.defineProperty(window, 'innerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'innerHeight', { get: () => 720, configurable: true });\n    Object.defineProperty(window, 'outerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'outerHeight', { get: () => 720, configurable: true });\n  } catch (err) {\n    console.debug('skip headless init adjustments', err);\n  }\n}");
                context.grantPermissions(Arrays.asList("geolocation", "notifications"));

                // 添加鼠标移动和键盘事件模拟
                context.addInitScript(
                        "() => {\n  try {\n    // 模拟鼠标移动 - 更真实的实现\n    let lastMouseX = 0;\n    let lastMouseY = 0;\n    let mouseIdleTime = 0;\n    \n    setInterval(() => {\n      // 随机决定是否移动鼠标（更真实的模拟）\n      if (Math.random() < 0.3) { // 30%的概率移动鼠标\n        const rect = document.documentElement.getBoundingClientRect();\n        const x = Math.random() * rect.width;\n        const y = Math.random() * rect.height;\n        \n        document.dispatchEvent(new MouseEvent('mousemove', {\n          clientX: x,\n          clientY: y,\n          bubbles: true,\n          cancelable: true\n        }));\n        \n        lastMouseX = x;\n        lastMouseY = y;\n        mouseIdleTime = 0;\n      } else {\n        mouseIdleTime++;\n      }\n    }, 1000 + Math.random() * 2000); // 1-3秒随机间隔\n    \n    // 模拟滚动 - 更真实的实现\n    let scrollPosition = 0;\n    setInterval(() => {\n      // 随机决定是否滚动（更真实的模拟）\n      if (Math.random() < 0.1) { // 10%的概率滚动\n        const scrollAmount = (Math.random() - 0.5) * 200; // -100到100的随机滚动量\n        const newPosition = Math.max(0, scrollPosition + scrollAmount);\n        \n        window.scrollTo({\n          top: newPosition,\n          behavior: 'smooth'\n        });\n        \n        scrollPosition = newPosition;\n      }\n    }, 3000 + Math.random() * 4000); // 3-7秒随机间隔\n    \n    // 模拟键盘活动\n    let keyPressCount = 0;\n    setInterval(() => {\n      // 偶尔模拟键盘按键（非常低的频率）\n      if (Math.random() < 0.02) { // 2%的概率按键\n        const key = String.fromCharCode(65 + Math.floor(Math.random() * 26)); // 随机字母\n        document.dispatchEvent(new KeyboardEvent('keydown', {\n          key: key,\n          code: 'Key' + key.toUpperCase(),\n          bubbles: true,\n          cancelable: true\n        }));\n        keyPressCount++;\n      }\n    }, 10000); // 10秒间隔\n  } catch (err) {\n    console.debug('skip mouse simulation', err);\n  }\n}");
            }

            page = context.newPage();
            
            // 设置网络监听器（如果启用）
            if (networkErrorDetection) {
                setupConsoleAndNetworkListeners(page);
            }
            
            Object webdriverValue = page.evaluate("() => navigator.webdriver");
            logger.info("navigator.webdriver: {}", webdriverValue);

            // 删除现有的会话文件（如果存在）
            if (Files.exists(storageFile)) {
                Files.deleteIfExists(storageFile);
                logger.info("已删除现有的会话文件: {}", storageFile);
            }

            // 直接进行登录流程
            logger.info("开始执行登录流程...");

            page.navigate("https://secure7.www.apple.com.cn/shop/account/home");

            // 等待页面加载并检查是否需要登录
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            login(page);

            // 保存会话状态
            saveSessionState(context, storageFile);
            logger.info("登录完成！会话文件已生成: {}", storageFile.toAbsolutePath());

            // 验证生成的会话
            if (isSessionValid(context)) {
                logger.info("会话验证成功！");
            } else {
                logger.warn("会话验证失败，可能需要重新登录");
            }

        } catch (Exception e) {
            logger.error("登录流程执行出错: {}", e.getMessage(), e);
        } finally {
            // 清理资源
            if (page != null) {
                try {
                    page.close();
                } catch (Exception e) {
                    logger.error("关闭页面时发生错误: {}", e.getMessage(), e);
                }
            }

            if (context != null) {
                try {
                    // 如果启用了视频录制，确保视频已保存
                    if (recordVideo) {
                        // 等待一小段时间确保视频文件写入完成
                        Thread.sleep(1000);
                        logger.info("视频录制已完成，保存在目录: {}", Paths.get(videoDir).toAbsolutePath());
                    }
                    context.close();
                    logger.info("浏览器上下文已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器上下文时出错: {}", e.getMessage(), e);
                }
            }

            if (browser != null) {
                try {
                    browser.close();
                    logger.info("浏览器已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器时出错: {}", e.getMessage(), e);
                }
            }

            if (playwright != null) {
                try {
                    playwright.close();
                    logger.info("Playwright 已关闭");
                } catch (Exception e) {
                    logger.error("关闭 Playwright 时出错: {}", e.getMessage(), e);
                }
            }
        }
    }

    public void startSingleMode() {
        Playwright playwright = null;
        Browser browser = null;
        BrowserContext context = null;
        Page page = null;
        Path storageFile = Paths.get(authFilePath);

        try {
            playwright = Playwright.create();

            if (usePersistentContext) {
                logger.info("使用持久化上下文模式启动浏览器");
                // 使用持久化上下文模式
                BrowserType.LaunchPersistentContextOptions launchOptions = new BrowserType.LaunchPersistentContextOptions()
                        .setHeadless(headless)
                        .setArgs(Arrays.asList(
                                "--disable-blink-features=AutomationControlled",
                                "--start-maximized",
                                "--disable-webrtc"))
                        .setViewportSize(null);

                // 持久化上下文模式下的视频录制配置
                if (recordVideo) {
                    logger.info("持久化上下文模式下启用视频录制，保存目录: {}", videoDir);
                    Path videoPath = Paths.get(videoDir);
                    try {
                        if (!Files.exists(videoPath)) {
                            Files.createDirectories(videoPath);
                        }
                        launchOptions.setRecordVideoDir(videoPath).setRecordVideoSize(1280, 720);
                        if (headless) {
                            launchOptions.setViewportSize(1280, 720);
                        }
                    } catch (Exception e) {
                        logger.warn("持久化上下文模式下设置视频录制失败: {}", e.getMessage());
                    }
                }

                // 检查是否存在已保存的会话状态
                if (Files.exists(storageFile)) {
                    logger.info("发现已保存的 Apple 登录状态，尝试复用会话...");
                }

                context = playwright.chromium().launchPersistentContext(
                        Paths.get("my-apple-session"), launchOptions);
                        
            } else {
                logger.info("使用普通浏览器模式启动");
                // 使用普通的浏览器启动模式
                BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                        .setHeadless(headless)
                        .setArgs(Arrays.asList(
                                "--disable-blink-features=AutomationControlled",
                                "--start-maximized",
                                "--disable-webrtc"))
                        .setTimeout(120000);

                browser = playwright.chromium().launch(launchOptions);
                        
                // 创建浏览器上下文，支持会话保存和读取
                Browser.NewContextOptions contextOptions = new Browser.NewContextOptions();
                
                // 检查是否存在已保存的会话状态
                if (Files.exists(storageFile)) {
                    logger.info("发现已保存的 Apple 登录状态，尝试复用会话...");
                    contextOptions.setStorageStatePath(storageFile);
                } else {
                    logger.info("未找到已保存的会话，将创建新的登录会话");
                }
                
                // 普通模式下的视频录制配置
                if (recordVideo) {
                    logger.info("启用视频录制，保存目录: {}", videoDir);
                    Path videoPath = Paths.get(videoDir);
                    try {
                        // 确保视频目录存在
                        if (!Files.exists(videoPath)) {
                            Files.createDirectories(videoPath);
                        }
                        contextOptions.setRecordVideoDir(videoPath).setRecordVideoSize(1280, 720);
                        if (headless) {
                            contextOptions.setViewportSize(1280, 720);
                        }
                    } catch (Exception e) {
                        logger.warn("设置视频录制失败: {}", e.getMessage());
                    }
                }
                
                // 普通模式下的headless环境参数
                if (headless) {
                    logger.info("检测到 headless 模式，应用额外的真实环境参数...");
                    String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                            + "AppleWebKit/537.36 (KHTML, like Gecko) "
                            + "Chrome/********* Safari/537.36";
                    contextOptions
                            .setUserAgent(realUserAgent)
                            .setLocale("zh-CN")
                            .setTimezoneId("Asia/Shanghai")
                            .setColorScheme(ColorScheme.LIGHT)
                            .setExtraHTTPHeaders(Map.of(
                                    "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                                    "Sec-CH-UA-Platform", "\"macOS\""))
                            .setGeolocation(new Geolocation(31.2304, 121.4737));
                }

                context = browser.newContext(contextOptions);
            }
            
            // 持久化上下文模式下的额外配置
            if (usePersistentContext) {
                // 持久化模式下的headless环境参数
                if (headless) {
                    logger.info("持久化上下文模式下应用真实环境参数...");
                    context.addInitScript(
                            "() => {\n  try {\n    Object.defineProperty(window, 'chrome', { value: { runtime: {} }, configurable: true });\n    Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5], configurable: true });\n    Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh'], configurable: true });\n    Object.defineProperty(navigator, 'platform', { get: () => 'MacIntel', configurable: true });\n    Object.defineProperty(navigator, 'cookieEnabled', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'onLine', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'userAgent', { get: () => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'vendor', { get: () => 'Google Inc.', configurable: true });\n    Object.defineProperty(navigator, 'appVersion', { get: () => '5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8, configurable: true });\n    Object.defineProperty(navigator, 'deviceMemory', { get: () => 8, configurable: true });\n    Object.defineProperty(screen, 'width', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'height', { get: () => 1080, configurable: true });\n    Object.defineProperty(screen, 'colorDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'pixelDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'availWidth', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'availHeight', { get: () => 1080, configurable: true });\n    Object.defineProperty(window, 'innerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'innerHeight', { get: () => 720, configurable: true });\n    Object.defineProperty(window, 'outerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'outerHeight', { get: () => 720, configurable: true });\n  } catch (err) {\n    console.debug('skip headless init adjustments', err);\n  }\n}");
                    context.grantPermissions(Arrays.asList("geolocation", "notifications"));

                    // 添加鼠标移动和键盘事件模拟
                    context.addInitScript(
                            "() => {\n  try {\n    // 模拟鼠标移动\n    let mouseMoveCount = 0;\n    setInterval(() => {\n      if (mouseMoveCount++ < 10) {\n        document.dispatchEvent(new MouseEvent('mousemove', {\n          clientX: Math.random() * window.innerWidth,\n          clientY: Math.random() * window.innerHeight\n        }));\n      }\n    }, 2000);\n    \n    // 模拟滚动\n    let scrollCount = 0;\n    setInterval(() => {\n      if (scrollCount++ < 5) {\n        window.scrollTo({ top: Math.random() * 500, behavior: 'smooth' });\n      }\n    }, 5000);\n  } catch (err) {\n    console.debug('skip mouse simulation', err);\n  }\n}");
                }
            }

            context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");

            context.addInitScript(
                    "() => {\n  try {\n    Object.defineProperty(Navigator.prototype, 'webdriver', {\n      get: () => undefined,\n      configurable: true\n    });\n  } catch (err) {\n    console.debug('skip webdriver override', err);\n  }\n}");

            if (headless) {
                context.addInitScript(
                        "() => {\n  try {\n    Object.defineProperty(window, 'chrome', { value: { runtime: {} }, configurable: true });\n    Object.defineProperty(navigator, 'plugins', { get: () => [1, 2, 3, 4, 5], configurable: true });\n    Object.defineProperty(navigator, 'languages', { get: () => ['zh-CN', 'zh'], configurable: true });\n    Object.defineProperty(navigator, 'platform', { get: () => 'MacIntel', configurable: true });\n    Object.defineProperty(navigator, 'cookieEnabled', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'onLine', { get: () => true, configurable: true });\n    Object.defineProperty(navigator, 'userAgent', { get: () => 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'vendor', { get: () => 'Google Inc.', configurable: true });\n    Object.defineProperty(navigator, 'appVersion', { get: () => '5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', configurable: true });\n    Object.defineProperty(navigator, 'hardwareConcurrency', { get: () => 8, configurable: true });\n    Object.defineProperty(navigator, 'deviceMemory', { get: () => 8, configurable: true });\n    Object.defineProperty(screen, 'width', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'height', { get: () => 1080, configurable: true });\n    Object.defineProperty(screen, 'colorDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'pixelDepth', { get: () => 24, configurable: true });\n    Object.defineProperty(screen, 'availWidth', { get: () => 1920, configurable: true });\n    Object.defineProperty(screen, 'availHeight', { get: () => 1080, configurable: true });\n    Object.defineProperty(window, 'innerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'innerHeight', { get: () => 720, configurable: true });\n    Object.defineProperty(window, 'outerWidth', { get: () => 1280, configurable: true });\n    Object.defineProperty(window, 'outerHeight', { get: () => 720, configurable: true });\n  } catch (err) {\n    console.debug('skip headless init adjustments', err);\n  }\n}");
                context.grantPermissions(Arrays.asList("geolocation", "notifications"));

                // 添加鼠标移动和键盘事件模拟
                context.addInitScript(
                        "() => {\n  try {\n    // 模拟鼠标移动 - 更真实的实现\n    let lastMouseX = 0;\n    let lastMouseY = 0;\n    let mouseIdleTime = 0;\n    \n    setInterval(() => {\n      // 随机决定是否移动鼠标（更真实的模拟）\n      if (Math.random() < 0.3) { // 30%的概率移动鼠标\n        const rect = document.documentElement.getBoundingClientRect();\n        const x = Math.random() * rect.width;\n        const y = Math.random() * rect.height;\n        \n        document.dispatchEvent(new MouseEvent('mousemove', {\n          clientX: x,\n          clientY: y,\n          bubbles: true,\n          cancelable: true\n        }));\n        \n        lastMouseX = x;\n        lastMouseY = y;\n        mouseIdleTime = 0;\n      } else {\n        mouseIdleTime++;\n      }\n    }, 1000 + Math.random() * 2000); // 1-3秒随机间隔\n    \n    // 模拟滚动 - 更真实的实现\n    let scrollPosition = 0;\n    setInterval(() => {\n      // 随机决定是否滚动（更真实的模拟）\n      if (Math.random() < 0.1) { // 10%的概率滚动\n        const scrollAmount = (Math.random() - 0.5) * 200; // -100到100的随机滚动量\n        const newPosition = Math.max(0, scrollPosition + scrollAmount);\n        \n        window.scrollTo({\n          top: newPosition,\n          behavior: 'smooth'\n        });\n        \n        scrollPosition = newPosition;\n      }\n    }, 3000 + Math.random() * 4000); // 3-7秒随机间隔\n    \n    // 模拟键盘活动\n    let keyPressCount = 0;\n    setInterval(() => {\n      // 偶尔模拟键盘按键（非常低的频率）\n      if (Math.random() < 0.02) { // 2%的概率按键\n        const key = String.fromCharCode(65 + Math.floor(Math.random() * 26)); // 随机字母\n        document.dispatchEvent(new KeyboardEvent('keydown', {\n          key: key,\n          code: 'Key' + key.toUpperCase(),\n          bubbles: true,\n          cancelable: true\n        }));\n        keyPressCount++;\n      }\n    }, 10000); // 10秒间隔\n  } catch (err) {\n    console.debug('skip mouse simulation', err);\n  }\n}");
            }

            page = context.newPage();
            
            // 设置控制台消息监听（如果启用）
            if (networkErrorDetection) {
                setupConsoleAndNetworkListeners(page);
            }
            
            Object webdriverValue = page.evaluate("() => navigator.webdriver");
            logger.info("navigator.webdriver: {}", webdriverValue);

            // 检查会话状态并决定流程
            boolean sessionValid = false;
            if (usePersistentContext) {
                // 持久化上下文模式，假设会话总是有效的（由Playwright自动管理）
                sessionValid = true;
                logger.info("持久化上下文模式，跳过会话验证");
            } else {
                // 普通模式，检查会话文件
                if (Files.exists(storageFile)) {
                    sessionValid = isSessionValid(context);
                    if (!sessionValid) {
                        logger.warn("保存的会话已失效，删除会话文件并重新开始");
                        try {
                            Files.deleteIfExists(storageFile);
                        } catch (Exception e) {
                            logger.warn("删除无效会话文件失败: {}", e.getMessage());
                        }
                    }
                }
            }

            if (sessionValid) {
                logger.info("使用有效会话，直接进行购物操作");
                // 会话有效，直接添加商品到购物车，不需要登录
                addIphone17ProToCart(page);
            } else {
                logger.info("会话无效或不存在，需要先登录");
                // 先添加商品到购物车（这会跳转到登录页面）
                addIphone17ProToCart(page);
                
                page.waitForLoadState(LoadState.DOMCONTENTLOADED);
                waitForCheckoutTransition(page);

                if (isCheckoutPage(page)) {
                    logger.info("检测到已在结账页面，跳过登录流程");
                } else {
                    login(page);
                }
            }

            

        } catch (Exception e) {
            logger.error("Apple 登录爬虫执行出错: {}", e.getMessage(), e);
        } finally {
            if (page != null) {
                try {
                    page.close();
                } catch (Exception e) {
                    logger.error("关闭页面时发生错误: {}", e.getMessage(), e);
                }
            }

            if (context != null) {
                try {
                    // 如果启用了视频录制，确保视频已保存
                    if (recordVideo) {
                        // 等待一小段时间确保视频文件写入完成
                        Thread.sleep(1000);
                        logger.info("视频录制已完成，保存在目录: {}", Paths.get(videoDir).toAbsolutePath());
                    }

                    // 只有在普通模式下才保存会话状态到文件
                    if (!usePersistentContext) {
                        saveSessionState(context, storageFile);
                    } else {
                        logger.info("持久化上下文模式，会话自动保存在 my-apple-session 目录");
                    }

                    context.close();
                    logger.info("浏览器上下文已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器上下文时出错: {}", e.getMessage(), e);
                }
            }

            if (browser != null) {
                try {
                    browser.close();
                    logger.info("浏览器已关闭");
                } catch (Exception e) {
                    logger.error("关闭浏览器时出错: {}", e.getMessage(), e);
                }
            }

            if (playwright != null) {
                try {
                    playwright.close();
                    logger.info("Playwright 已关闭");
                } catch (Exception e) {
                    logger.error("关闭 Playwright 时出错: {}", e.getMessage(), e);
                }
            }
        }
    }



    private void login(Page page) {
        logger.info("开始登录 Apple 账号...");
        FrameLocator loginFrame = waitForLoginFrame(page);
        Locator accountField = loginFrame.locator("input#account_name_text_field");
        try {
            accountField.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(60_000));
        } catch (Exception e) {
            logger.error("用户名输入框未在预期时间内出现: {}", e.getMessage());
            captureDebugScreenshot(page, "login-account-field-timeout");
            throw new IllegalStateException("未能加载 Apple 登录用户名输入框", e);
        }
        typeText(accountField, this.username);
        logger.debug("已填写用户名");
        clickSignIn(page, loginFrame, accountField);

        Locator passwordField = loginFrame.locator("input#password_text_field");
        try {
            passwordField.waitFor(new Locator.WaitForOptions()
                    .setState(WaitForSelectorState.VISIBLE)
                    .setTimeout(60_000));
        } catch (Exception e) {
            logger.error("密码输入框未在预期时间内出现: {}", e.getMessage());
            captureDebugScreenshot(page, "login-password-field-timeout");
            throw new IllegalStateException("未能加载 Apple 登录密码输入框", e);
        }
        typeText(passwordField, this.password);
        logger.debug("已填写密码");
        clickSignIn(page, loginFrame, passwordField);

        page.waitForLoadState(LoadState.NETWORKIDLE);
        logger.info("Apple 登录流程已提交");
        
        // 登录完成后，自动保存会话状态
        try {
            // 等待一下确保登录状态完全生效
            Thread.sleep(2000);
            
            // 获取当前的浏览器上下文并保存会话
            BrowserContext context = page.context();
            Path storageFile = Paths.get(authFilePath);
            
            // 只在普通模式下保存会话文件
            if (!usePersistentContext) {
                saveSessionState(context, storageFile);
                logger.info("登录成功，会话状态已自动保存到: {}", storageFile.toAbsolutePath());
            } else {
                logger.info("持久化上下文模式，会话自动保存在 my-apple-session 目录");
            }
            
        } catch (Exception e) {
            logger.warn("保存登录会话状态失败: {}", e.getMessage(), e);
        }
    }

    private boolean needsFreshLogin(Page page) {
        if (page.url().startsWith(APPLE_ACCOUNT_HOME)) {
            return false;
        }

        if (page.url().startsWith(APPLE_SIGN_IN_PREFIX)) {
            return true;
        }

        return page.frameLocator("iframe#aid-auth-widget-iFrame").locator("input#account_name_text_field").count() > 0
                || page.locator("text=登录以继续").count() > 0;
    }

    private boolean isCheckoutPage(Page page) {
        String currentUrl = page.url();
        logger.info("当前页面 URL: {}", currentUrl);
        if (currentUrl != null && currentUrl.contains("/shop/checkout")) {
            return true;
        }

        try {
            Locator container = page.locator("#checkout-container");
            container.first()
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(5000));

            if (container.count() == 0) {
                return false;
            }

            Locator header = container.locator("div.rs-checkout-headerbar-title");
            if (header.count() == 0) {
                return false;
            }

            String titleText = header.first().innerText();
            return titleText != null && titleText.contains("结账");
        } catch (Exception e) {
            logger.debug("未能定位结账标题: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查页面是否是登录页面
     * 通过检测是否存在登录iframe来判断
     */
    private boolean isLoginPage(Page page) {
        try {
            // 首先检查页面是否还在加载中
            if (page.isClosed()) {
                logger.warn("页面已关闭，无法检查登录状态");
                return false;
            }
            
            // 检查URL是否包含登录相关路径（最可靠的方法）
            String currentUrl = page.url();
            if (currentUrl.contains("/signIn") || currentUrl.contains("/auth/") || 
                currentUrl.contains("appleid.apple.com") || currentUrl.contains("idmsa.apple.com")) {
                logger.info("检测到登录URL: {}", currentUrl);
                return true;
            }

            // 检查是否存在登录iframe（需要等待页面稳定）
            try {
                Locator loginIframe = page.locator("iframe#aid-auth-widget-iFrame");
                int iframeCount = loginIframe.count();
                if (iframeCount > 0) {
                    logger.info("检测到登录iframe，当前页面是登录页面");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("检查登录iframe时出错: {}", e.getMessage());
            }

            // 检查是否包含登录相关的文本
            try {
                if (page.locator("text=登录你的 Apple").count() > 0 ||
                    page.locator("text=登录以继续").count() > 0 ||
                    page.locator("text=Apple 账户").count() > 0 ||
                    page.locator("text=Sign in").count() > 0) {
                    logger.info("检测到登录相关文本，当前页面是登录页面");
                    return true;
                }
            } catch (Exception e) {
                logger.debug("检查登录文本时出错: {}", e.getMessage());
            }

            logger.debug("未检测到登录页面特征，继续正常流程");
            return false;

        } catch (Exception e) {
            logger.warn("检查登录页面时出错: {}", e.getMessage());
            // 如果出现执行上下文错误，返回false让程序继续
            return false;
        }
    }

    private void waitForCheckoutTransition(Page page) {
        try {
            page.waitForURL(url -> url != null
                    && (url.contains("/shop/checkout")
                            || url.contains("signin.apple.com")
                            || url.contains("/shop/checkout/guest")),
                    new Page.WaitForURLOptions().setTimeout(15_000));
        } catch (Exception e) {
            logger.debug("在 15 秒内未检测到结账跳转，当前 URL: {}", page.url());
        }
    }

    private void captureAccountScreenshot(Page page, String suffix) {
        saveScreenshot(page, "apple-account-" + suffix, "登录后的页面截图已保存到: {}");
    }

    private void captureDebugScreenshot(Page page, String suffix) {
        saveScreenshot(page, "apple-debug-" + suffix, "调试截图已保存到: {}");
    }

    private void saveScreenshot(Page page, String filePrefix, String logTemplate) {
        try {
            File dir = new File(screenshotDir);
            if (!dir.exists() && dir.mkdirs()) {
                logger.info("创建截图目录: {}", dir.getAbsolutePath());
            }

            if (!dir.exists()) {
                logger.warn("截图目录不存在且无法创建: {}", dir.getAbsolutePath());
                return;
            }

            String timestamp = new java.text.SimpleDateFormat("yyyyMMdd_HHmmss").format(new java.util.Date());
            Path screenshotPath = Paths.get(dir.getAbsolutePath(), filePrefix + "-" + timestamp + ".png");
            page.waitForTimeout(500);
            page.screenshot(new Page.ScreenshotOptions().setPath(screenshotPath).setFullPage(true));
            logger.info(logTemplate, screenshotPath);
        } catch (Exception e) {
            logger.error("保存截图失败 [{}]: {}", filePrefix, e.getMessage(), e);
        }
    }

    private FrameLocator waitForLoginFrame(Page page) {
        try {
            page.waitForSelector("iframe#aid-auth-widget-iFrame",
                    new Page.WaitForSelectorOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(60_000));
        } catch (Exception e) {
            captureDebugScreenshot(page, "login-iframe-timeout");
            throw new IllegalStateException("未找到 Apple 登录 iframe，可能页面结构已变更", e);
        }
        return page.frameLocator("iframe#aid-auth-widget-iFrame");
    }

    private void waitForAccountHome(Page page) {
        try {
            page.waitForURL(url -> url != null && url.startsWith(APPLE_ACCOUNT_HOME),
                    new Page.WaitForURLOptions().setTimeout(120_000));
        } catch (Exception e) {
            logger.warn("未能确认跳转到 Apple 账户首页，当前 URL: {}", page.url());
        }
    }

    private void clickSignIn(Page page, FrameLocator loginFrame, Locator sourceField) {
        Locator signInButton = loginFrame.locator(
                "button#sign-in:not([aria-disabled='true']):visible, button[type='submit']:not([aria-disabled='true']):visible");

        try {
            if (signInButton.count() > 0) {
                Locator button = signInButton.first();
                button.waitFor();
                button.scrollIntoViewIfNeeded();
                try {
                    button.click();
                } catch (Exception primaryClickError) {
                    logger.debug("常规点击失败，尝试强制点击: {}", primaryClickError.getMessage());
                    button.click(new Locator.ClickOptions().setForce(true));
                }
            } else {
                sourceField.press("Enter");
            }
        } catch (Exception clickError) {
            logger.debug("点击登录按钮失败，尝试回车提交: {}", clickError.getMessage());
            try {
                sourceField.press("Enter");
            } catch (Exception pressError) {
                throw new IllegalStateException("无法提交 Apple 登录表单", pressError);
            }
        }

        try {
            sourceField.press("Tab");
        } catch (Exception ignore) {
            // 如果无法发送 Tab，则忽略并继续
        }
        page.waitForTimeout(500);
    }

    private void typeText(Locator field, String value) {
        try {
            field.click(new Locator.ClickOptions().setTimeout(10_000));
            field.fill("");
            field.type(value, new Locator.TypeOptions().setDelay(50));
        } catch (Exception e) {
            throw new IllegalStateException("向输入框写入文本失败", e);
        }
    }

    public boolean checkIphone17ProStock(Page page) {
        try {
            logger.info("开始监控 iPhone 17 Pro 昆明库存...");

            // 导航到iPhone购买页面
            page.navigate(iphoneConfig.getBuyUrl());
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            // 选择iPhone型号 - 添加随机延迟
            Locator proOption = page.locator("span.form-selector-title",
                    new Page.LocatorOptions().setHasText(iphoneConfig.getModelDisplayName()));
            proOption.first().waitFor();
            proOption.first().click();
            page.waitForTimeout(1000 + (long)(Math.random() * 1000)); // 1-2秒随机延迟

            // 选择颜色 - 添加随机延迟
            logger.info("尝试选择颜色: {}", iphoneConfig.getColorDisplayName());
            ConfigValidator.printUnicodeInfo(iphoneConfig.getColorDisplayName());
            
            page.getByRole(AriaRole.LISTITEM)
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getColorDisplayName()))
                    .locator("img")
                    .click();
            page.waitForTimeout(1500 + (long)(Math.random() * 1000)); // 1.5-2.5秒随机延迟

            // 选择存储容量 - 添加随机延迟
            page.locator("#root span")
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getStorageDisplayName()))
                    .nth(1)
                    .click();
            page.waitForTimeout(1200 + (long)(Math.random() * 800)); // 1.2-2秒随机延迟

            // 选择折抵换购选项 - 添加随机延迟
            if (iphoneConfig.isTradeIn()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("折抵换购")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不折抵换购")).check();
            }
            page.waitForTimeout(800 + (long)(Math.random() * 600)); // 0.8-1.4秒随机延迟
            
            // 选择AppleCare+选项 - 添加随机延迟
            if (iphoneConfig.isAppleCare()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            page.waitForTimeout(1000 + (long)(Math.random() * 800)); // 1-1.8秒随机延迟

            logger.info("检查是否显示昆明取货信息...");
            Locator fulfillmentInfo = page.locator(".rf-bfe-summary-fulfillment-fullWidth");
            fulfillmentInfo
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

            logger.info("检查是否显示昆明取货信息...");
            // 等待并检查是否有昆明取货选项
            try {
                Locator kunmingButton = page.locator("button.rf-pickup-quote-overlay-trigger").filter(new Locator.FilterOptions().setHasText("Apple 昆明")).first();
                kunmingButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
                logger.info("找到昆明取货选项");

                // 检查是否显示"立即订购。取货 (店内)"标识
                Locator pickupLabel = page.locator("text=立即订购。取货 (店内)");
                pickupLabel.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(5000));
                logger.info("监控发现 iphone17'立即订购。取货 (店内)'标识，可以进行下单");
                return true; // 有库存


            } catch (Exception e) {
                logger.warn("未找到完整的取货信息，无法进行下单: " + e.getMessage());
                captureDebugScreenshot(page, "kunming-pickup-missing");
                return false;
            }

        } catch (Exception e) {
            logger.error("iPhone 17 Pro 选购流程执行失败: {}", e.getMessage(), e);
            captureDebugScreenshot(page, "add-to-cart-failed");
        }
        
        return false; // 默认返回无库存
    }

    private void addIphone17ProToCart(Page page) {
        try {
            logger.info("开始执行 iPhone 17 Pro 选购流程...");

            // 导航到iPhone购买页面
            page.navigate(iphoneConfig.getBuyUrl());
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            // 选择iPhone型号 - 添加随机延迟
            Locator proOption = page.locator("span.form-selector-title",
                    new Page.LocatorOptions().setHasText(iphoneConfig.getModelDisplayName()));
            proOption.first().waitFor();
            proOption.first().click();
            page.waitForTimeout(1000 + (long)(Math.random() * 1000)); // 1-2秒随机延迟

            // 选择颜色 - 添加随机延迟
            logger.info("尝试选择颜色: {}", iphoneConfig.getColorDisplayName());
            ConfigValidator.printUnicodeInfo(iphoneConfig.getColorDisplayName());
            
            page.getByRole(AriaRole.LISTITEM)
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getColorDisplayName()))
                    .locator("img")
                    .click();
            page.waitForTimeout(1500 + (long)(Math.random() * 1000)); // 1.5-2.5秒随机延迟

            // 选择存储容量 - 添加随机延迟
            page.locator("#root span")
                    .filter(new Locator.FilterOptions().setHasText(iphoneConfig.getStorageDisplayName()))
                    .nth(1)
                    .click();
            page.waitForTimeout(1200 + (long)(Math.random() * 800)); // 1.2-2秒随机延迟

            // 选择折抵换购选项 - 添加随机延迟
            if (iphoneConfig.isTradeIn()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("折抵换购")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不折抵换购")).check();
            }
            page.waitForTimeout(800 + (long)(Math.random() * 600)); // 0.8-1.4秒随机延迟
            
            // 选择AppleCare+选项 - 添加随机延迟
            if (iphoneConfig.isAppleCare()) {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("加 AppleCare+ 服务计划")).check();
            } else {
                page.getByRole(AriaRole.RADIO, new Page.GetByRoleOptions().setName("不加 AppleCare+ 服务计划")).check();
            }
            page.waitForTimeout(1000 + (long)(Math.random() * 800)); // 1-1.8秒随机延迟

            logger.info("检查是否显示昆明取货信息...");
            Locator fulfillmentInfo = page.locator(".rf-bfe-summary-fulfillment-fullWidth");
            fulfillmentInfo
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

            logger.info("检查是否显示昆明取货信息...");
            // 等待并检查是否有昆明取货选项
            try {
                Locator kunmingButton = page.locator("button.rf-pickup-quote-overlay-trigger").filter(new Locator.FilterOptions().setHasText("Apple 昆明")).first();
                kunmingButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
                logger.info("找到昆明取货选项");

                // 检查是否显示"立即订购。取货 (店内)"标识
                Locator pickupLabel = page.locator("text=立即订购。取货 (店内)");
                pickupLabel.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(5000));
                logger.info("找到'立即订购。取货 (店内)'标识，可以进行下单");

                page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("添加到购物袋")).click();
                page.waitForTimeout(2000 + (long)(Math.random() * 1500)); // 2-3.5秒随机延迟
                logger.info("iPhone 17 Pro 已成功加入购物袋");
                page.waitForLoadState(LoadState.NETWORKIDLE);

                // 成功添加商品后，继续后续流程
                continueWithShoppingFlow(page);

            } catch (Exception e) {
                logger.warn("未找到完整的取货信息，无法进行下单: " + e.getMessage());
                captureDebugScreenshot(page, "kunming-pickup-missing");
                return;
            }

        } catch (Exception e) {
            logger.error("iPhone 17 Pro 选购流程执行失败: {}", e.getMessage(), e);
            captureDebugScreenshot(page, "add-to-cart-failed");
        }
    }

    /**
     * 继续购物流程（查看购物袋、调整数量、结账等）
     */
    private void continueWithShoppingFlow(Page page) throws InterruptedException {
        logger.info("继续购物流程...");

        // 查看购物袋
        page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("查看购物袋")).click();
        page.waitForLoadState(LoadState.NETWORKIDLE);

        // 调整商品数量为1
        Locator quantitySelect = page.locator("select.rs-quantity-dropdown[id*='itemQuantity']");
        if (quantitySelect.count() > 0) {
            Locator firstDropdown = quantitySelect.first();
            firstDropdown.waitFor(
                    new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
            firstDropdown.selectOption("1");
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("已通过下拉框将商品数量更新为 1");
        } else {
            Locator quantityInput = page.locator("input[id*='itemQuantity']");
            if (quantityInput.count() > 0) {
                Locator firstQuantityInput = quantityInput.first();
                firstQuantityInput.waitFor(
                        new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(10000));
                firstQuantityInput.fill("1");
                firstQuantityInput.press("Enter");
                page.waitForLoadState(LoadState.NETWORKIDLE);
                logger.info("已将商品数量更新为 1");
            }
        }

        // 结账
        logger.info("点击结账按钮...");
        page.getByRole(AriaRole.BUTTON, new Page.GetByRoleOptions().setName("结账")).first().click();
        
        // 等待页面导航完成，使用更稳定的等待策略
        try {
            // 等待页面开始导航
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            // 额外等待确保页面完全加载
            page.waitForTimeout(2000 + (long)(Math.random() * 1000)); // 2-3秒随机延迟
            
            logger.info("检查页面是否跳转到登录页面...");
            // 检查是否跳转到了登录页面
            if (isLoginPage(page)) {
                logger.info("检测到登录页面，开始登录流程...");
                login(page);
                checkout(page);
            } else {
                logger.info("未检测到登录页面，继续结账流程...");
                checkout(page);
            }
        } catch (Exception e) {
            logger.warn("检查登录页面时出错: {}", e.getMessage());
            // 如果检查失败，尝试直接进行结账流程
            logger.info("尝试直接进行结账流程...");
            checkout(page);
        }
    }
    

    private void checkout(Page page) {
        try {
            logger.info("开始执行结账流程...");

            // 查找取货按钮 - 使用多种方式确保能找到
            Locator pickupButton = page.locator("button.rc-segmented-control-button").filter(new Locator.FilterOptions().setHasText("我要取货"));
            pickupButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

            // 检查按钮是否已经被选中
            String ariaChecked = pickupButton.getAttribute("aria-checked");
            if (ariaChecked == null || !ariaChecked.equals("true")) {
                logger.info("点击我要取货按钮...");
                pickupButton.click();
                page.waitForTimeout(1000); // 等待按钮点击效果
            } else {
                logger.info("我要取货按钮已经被选中，跳过点击");
            }

            logger.info("已处理我要取货选项...");

            Locator locationToggle = page
                    .locator("button[data-autom='fulfillment-pickup-store-search-button']");
            boolean locationPreset = isTargetLocation(locationToggle, "云南");

            if (!locationPreset) {
                ensureLocationSelectorOpen(page, locationToggle);

                Locator tabList = page.locator(".rc-province-selector-tablist");
                tabList.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

                selectRegionOption(page, locationToggle, "state",
                        ".rc-province-selector-option-state", "云南");
                logger.info("选中云南...");

                selectRegionOption(page, locationToggle, "city",
                        ".rc-province-selector-option-city", "昆明");
                logger.info("选中昆明...");

                selectRegionOption(page, locationToggle, "district",
                        ".rc-province-selector-option-district", "五华区");
                logger.info("选中五华区...");
            } else {
                logger.info("当前已处于云南 昆明，跳过地区选择。");
            }


            logger.info("地区选择完毕...");
            // page.waitForTimeout(5000);

            selectStoreOption(page, "Apple 昆明", "R670");

            logger.info("⏳ 等待门店选择后的页面加载...");
            // 等待页面完全加载
            page.waitForLoadState(LoadState.NETWORKIDLE);
            page.waitForTimeout(2000); // 额外等待2秒确保页面稳定
            
            logger.info("✅ 门店选择完成，开始进入取货详情页面...");
            
            Locator continueButton = page.getByRole(AriaRole.BUTTON,
                    new Page.GetByRoleOptions().setName("继续填写取货详情"));
            continueButton.waitFor(
                    new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            continueButton.scrollIntoViewIfNeeded();
            continueButton.click();
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("结账流程执行完毕，已进入下一步");

            fillPickupDetails(page);

            selectPaymentMethod(page);

        } catch (Exception e) {
            logger.warn("结帐流程执行失败: {}", e.getMessage(), e);
        }
    }

    private void fillPickupDetails(Page page) {
        try {
            logger.info("📝 开始填写取货详情...");
            
            // 等待页面完全加载
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);
            page.waitForLoadState(LoadState.NETWORKIDLE);
            page.waitForTimeout(1000); // 额外等待确保页面稳定
            
            logger.info("🔍 等待取货详情表单加载...");
            Locator lastName = page.locator("input[name='lastName']");
            lastName.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到姓氏输入框，开始填写...");
            lastName.fill(userConfig.getLastName());

            Locator firstName = page.locator("input[name='firstName']");
            firstName.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到名字输入框，开始填写...");
            firstName.fill(userConfig.getFirstName());

            Locator phone = page.locator("input[name='fullDaytimePhone']");
            phone.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            logger.info("✅ 找到电话输入框，开始填写...");
            phone.fill(userConfig.getPhone());

            Locator email = page.locator("input[name='emailAddress']");
            email.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            email.fill(userConfig.getEmail());

            Locator receiptRadio = page
                    .locator("input[name='checkout.pickupContact.eFapiaoSelector.selectFapiao'][value='none']");
            receiptRadio
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(15000));
            if (!receiptRadio.isChecked()) {
                receiptRadio.check();
            }

            Locator continueButton = page.getByRole(AriaRole.BUTTON,
                    new Page.GetByRoleOptions().setName("继续选择付款方式"));
            continueButton
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            continueButton.scrollIntoViewIfNeeded();
            continueButton.click();
            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("取货信息填写完毕，已进入付款方式选择");
            selectPaymentMethod(page);
        } catch (Exception e) {
            logger.warn("填写取货信息失败: {}", e.getMessage(), e);
        }
    }

    private void selectPaymentMethod(Page page) {
        try {
            page.waitForLoadState(LoadState.DOMCONTENTLOADED);

            Locator alipayLabel = page.locator("label[for='checkout.billing.billingoptions.alipay']");
            alipayLabel
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));

            Locator alipayRadio = page.locator("input#checkout\\.billing\\.billingoptions\\.alipay");
            alipayRadio.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(20000));

            if (!alipayRadio.isChecked()) {
                try {
                    alipayRadio.check();
                } catch (Exception e) {
                    logger.debug("直接选中支付宝失败，尝试点击标签: {}", e.getMessage());
                    try {
                        alipayLabel.click();
                    } catch (Exception ignore) {
                        alipayLabel.click(new Locator.ClickOptions().setForce(true));
                    }
                    page.waitForTimeout(200);

                    if (!alipayRadio.isChecked()) {
                        page.evaluate(
                                "radio => {\n                            radio.checked = true;\n                            radio.dispatchEvent(new Event('input', { bubbles: true }));\n                            radio.dispatchEvent(new Event('change', { bubbles: true }));\n                        }",
                                alipayRadio);
                        page.waitForTimeout(200);
                    }
                }
            }

            if (!alipayRadio.isChecked()) {
                logger.warn("支付宝支付方式仍未选中，请人工确认。");
            }

            Locator reviewButton = page.locator("#rs-checkout-continue-button-bottom");
            reviewButton.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
            reviewButton.scrollIntoViewIfNeeded();
            try {
                reviewButton.click();
            } catch (Exception ignore) {
                reviewButton.click(new Locator.ClickOptions().setForce(true));
            }

            page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("已选择支付宝并点击检查订单");

            // Locator placeOrderButton =
            // page.locator("#rs-checkout-continue-button-bottom",
            // new Page.LocatorOptions().setHasText("立即下单"));
            // placeOrderButton
            // .waitFor(new
            // Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(30000));
            // placeOrderButton.scrollIntoViewIfNeeded();
            // try {
            // placeOrderButton.click();
            // } catch (Exception ignore) {
            // placeOrderButton.click(new Locator.ClickOptions().setForce(true));
            // }

            // page.waitForLoadState(LoadState.NETWORKIDLE);
            logger.info("已点击立即下单");
            captureAccountScreenshot(page, "end-order");

        } catch (Exception e) {
            logger.warn("选择付款方式失败: {}", e.getMessage(), e);
        }
    }

    private boolean isTargetLocation(Locator locationToggle, String... tokens) {
        try {
            locationToggle
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
            Locator firstSpan = locationToggle.locator("span");
            String textContent = null;
            if (firstSpan.count() > 0) {
                textContent = firstSpan.first().innerText();
            }
            if (textContent == null || textContent.isEmpty()) {
                textContent = locationToggle.innerText();
            }
            if (textContent != null) {
                for (String token : tokens) {
                    if (!textContent.contains(token)) {
                        return false;
                    }
                }
                return true;
            }
        } catch (Exception e) {
            logger.debug("读取当前位置文案失败，继续执行地区选择: {}", e.getMessage());
        }
        return false;
    }

    private void ensureLocationSelectorOpen(Page page, Locator locationToggle) {
        locationToggle.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
        String expanded = locationToggle.getAttribute("aria-expanded");
        if (!"true".equalsIgnoreCase(expanded)) {
            locationToggle.scrollIntoViewIfNeeded();
            try {
                locationToggle.click();
            } catch (Exception ignore) {
                locationToggle.click(new Locator.ClickOptions().setForce(true));
            }
            page.waitForTimeout(200);
        }

            // 保存位置信息到浏览器存储
            try {
                Locator saveLocationCheckbox = page.getByLabel("保存我的位置信息以便日后使用");
                saveLocationCheckbox.waitFor(new Locator.WaitForOptions()
                        .setState(WaitForSelectorState.ATTACHED)
                        .setTimeout(10000));
                if (!saveLocationCheckbox.isChecked()) {
                    saveLocationCheckbox.check(new Locator.CheckOptions().setForce(true));
                    logger.info("已勾选'保存我的位置信息以便日后使用'");
                }
                
                // 等待位置信息保存到浏览器存储
                page.waitForTimeout(2000);
                
                // 更新用户的会话文件，保存地区选择信息
                updateUserSessionWithLocation();
                
            } catch (Exception e) {
                logger.warn("保存位置信息失败，但继续执行: {}", e.getMessage());
            }

            
    }

    private void selectRegionOption(Page page, Locator locationToggle, String panelName, String optionSelector,
            String optionText) {
        ensureLocationSelectorOpen(page, locationToggle);

        Locator targetTab = getRegionTab(page, panelName);
        if (targetTab == null) {
            logger.warn("未找到地区级别选项卡: {}", panelName);
            return;
        }

        targetTab.scrollIntoViewIfNeeded();
        String selectedAttr = targetTab.getAttribute("aria-selected");
        if (!"true".equalsIgnoreCase(selectedAttr)) {
            try {
                targetTab.click();
            } catch (Exception ignore) {
                targetTab.click(new Locator.ClickOptions().setForce(true));
            }
            page.waitForTimeout(200);
        }

        Locator activePanel = page.locator(
                ".rc-province-selector-tab-panel[name='" + panelName + "'][data-core-tabs-panel-selected]");
        activePanel.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));

        Locator selectedOption = activePanel
                .locator(optionSelector + ".rc-province-selector-option-selected")
                .filter(new Locator.FilterOptions().setHasText(optionText));

        Locator optionNode = activePanel.locator(optionSelector)
                .filter(new Locator.FilterOptions().setHasText(optionText))
                .first();
        optionNode.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
        optionNode.scrollIntoViewIfNeeded();
        try {
            optionNode.click();
        } catch (Exception ignore) {
            optionNode.click(new Locator.ClickOptions().setForce(true));
        }

        selectedOption.waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(20000));
        page.waitForTimeout(300);
    }

    private void selectStoreOption(Page page, String storeName, String storeValue) {
        logger.info("🏪 开始选择门店: {} (value: {})", storeName, storeValue);
        page.waitForTimeout(1000);

        Locator storeContainer = page.locator("div.rs-store-locator");
        storeContainer.waitFor(new Locator.WaitForOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(20000));
        
        int totalStores = storeContainer.locator(".form-selector-input").count();
        logger.info("📊 门店容器已加载，总门店数量: {}", totalStores);

        // 等待门店列表加载
        Locator storeList = storeContainer.locator(".rt-storelocator-store-location");
        storeList.first().waitFor(new Locator.WaitForOptions()
                .setState(WaitForSelectorState.VISIBLE)
                .setTimeout(20000));

        boolean selected = false;
        
        // 方法1：通过 value 属性直接定位（最有效的方法）
        if (storeValue != null && !storeValue.isEmpty()) {
            logger.info("🎯 方法1：通过 value='{}' 定位门店", storeValue);
            Locator radioByValue = storeContainer.locator("input.form-selector-input[value='" + storeValue + "']");
            int matchCount = radioByValue.count();
            logger.info("📋 匹配到的门店数量: {}", matchCount);
            
            if (matchCount > 0) {
                try {
                    Locator targetRadio = radioByValue.first();
                    targetRadio.waitFor(
                            new Locator.WaitForOptions().setState(WaitForSelectorState.ATTACHED).setTimeout(20000));
                    
                    String radioId = targetRadio.getAttribute("id");
                    boolean disabled = targetRadio.isDisabled();
                    boolean checked = targetRadio.isChecked();
                    logger.info("✅ 找到目标门店: id='{}', disabled={}, checked={}", radioId, disabled, checked);
                    
                    if (!disabled) {
                        selected = checkStoreRadio(page, targetRadio, storeName, storeValue);
                        logger.info("📊 方法1选择结果: {}", selected ? "成功" : "失败");
                    } else {
                        logger.warn("⚠️ 门店不可用: {} ({})", storeName, storeValue);
                    }
                } catch (PlaywrightException e) {
                    logger.warn("❌ 方法1失败: {}", e.getMessage());
                }
            } else {
                logger.warn("❌ 未找到 value='{}' 的门店", storeValue);
            }
        } else {
            logger.info("ℹ️ 未提供 storeValue，跳过方法1");
        }

        if (selected) {
            logger.info("🎉 门店选择成功！");
            page.waitForTimeout(500);
            return;
        }

        // 如果方法1失败，记录错误信息
        logger.error("❌ 门店选择失败: {} (value: {})", storeName, storeValue);
        logger.info("💡 建议检查门店配置或网络连接");
    }

    private boolean checkStoreRadio(Page page, Locator radio, String storeName, String storeValue) {
        if (radio.isDisabled() || !radio.isEnabled()) {
            logger.debug("单选框无法点击，id={}, disabled={}, enabled={}", radio.getAttribute("id"),
                    radio.isDisabled(), radio.isEnabled());
            return false;
        }

        if (!radio.isChecked()) {
            try {
                radio.scrollIntoViewIfNeeded();
                radio.click(new Locator.ClickOptions().setForce(true));
                page.waitForTimeout(200);
            } catch (Exception clickEx) {
                logger.debug("radio.click(force) 失败: {}", clickEx.getMessage());
            }
        }

        if (!radio.isChecked()) {
            String radioId = radio.getAttribute("id");
            if (radioId != null && !radioId.isEmpty()) {
                Locator label = page.locator("label[for='" + radioId + "']");
                logger.debug("radio 未选中，尝试点击对应标签，label 数量={}", label.count());
                if (label.count() > 0) {
                    try {
                        label.first().click(new Locator.ClickOptions().setForce(true));
                    } catch (Exception ignore) {
                        logger.warn("点击门店标签失败: {}", ignore.getMessage());
                    }
                    page.waitForTimeout(200);
                    if (!radio.isChecked()) {
                        try {
                            page.evaluate(
                                    "({ id }) => { const label = document.querySelector(`label[for='${id}']`); if (label) label.click(); }",
                                    Map.of("id", radioId));
                            page.waitForTimeout(200);
                        } catch (Exception evalLabelEx) {
                            logger.debug("evaluate label.click() 失败: {}", evalLabelEx.getMessage());
                        }
                    }
                }
            }
        }

        if (!radio.isChecked()) {
            logger.debug("标签点击后仍未选中，尝试 evaluate 设置 checked，id={}", radio.getAttribute("id"));
            try {
                ElementHandle handle = radio.elementHandle();
                if (handle != null) {
                    handle.evaluate(
                            "el => {\n                el.checked = true;\n                el.dispatchEvent(new Event('input', { bubbles: true }));\n                el.dispatchEvent(new Event('change', { bubbles: true }));\n            }");
                } else if (storeValue != null) {
                    logger.debug("radio.elementHandle() 返回 null，尝试根据 value 设置");
                    page.evaluate(
                            "({ value }) => { const input = document.querySelector(`input.form-selector-input[value='${value}']`); if (input) { input.checked = true; input.dispatchEvent(new Event('input', { bubbles: true })); input.dispatchEvent(new Event('change', { bubbles: true })); } }",
                            Map.of("value", storeValue));
                }
            } catch (Exception evalEx) {
                logger.debug("evaluate 设置 checked 失败: {}", evalEx.getMessage());
            }
            page.waitForTimeout(200);
        }

        boolean checked = radio.isChecked();
        logger.debug("最终单选框状态，id={}，checked={}", radio.getAttribute("id"), checked);
        if (!checked) {
            logger.warn("门店单选框仍未选中，storeName={}, storeValue={}, id={}", storeName, storeValue,
                    radio.getAttribute("id"));
        }
        return checked;
    }

    private Locator getRegionTab(Page page, String panelName) {
        int index;
        switch (panelName) {
            case "state":
                index = 0;
                break;
            case "city":
                index = 1;
                break;
            case "district":
                index = 2;
                break;
            default:
                logger.warn("未知的地区级别: {}", panelName);
                return null;
        }

        Locator tabs = page.locator(".rc-province-selector-tablist button[role='tab']");
        try {
            tabs.nth(index)
                    .waitFor(new Locator.WaitForOptions().setState(WaitForSelectorState.VISIBLE).setTimeout(15000));
        } catch (Exception e) {
            logger.warn("地区级别选项卡未展示: {}", panelName, e);
            return null;
        }
        if (tabs.count() <= index) {
            logger.warn("地区级别选项卡数量不足，无法定位 {}", panelName);
            return null;
        }
        return tabs.nth(index);
    }

    
    /**
     * 设置控制台消息和网络请求监听器
     * 
     * @param page 页面对象
     */
    private void setupConsoleAndNetworkListeners(Page page) {
        // 监听控制台消息
        page.onConsoleMessage(msg -> {
            String text = msg.text();
            if (text.contains("514") || text.contains("Server Error") || text.contains("服务器错误")) {
                logger.warn("控制台检测到服务器错误: {}", text);
                http514ErrorDetected = true;
            }
        });
        
        // 监听网络响应
        page.onResponse(response -> {
            int status = response.status();
            String url = response.url();
            
            if (status == 514) {
                logger.warn("检测到HTTP 514错误，URL: {}", url);
                http514ErrorDetected = true;
            } else if (status >= 500) {
                logger.warn("检测到服务器错误 {}: {}", status, url);
                // 其他5xx错误也可能需要刷新
                if (url.contains("apple.com") && (status == 502 || status == 503 || status == 504)) {
                    http514ErrorDetected = true;
                }
            }
        });
        
        // 监听网络请求失败
        page.onRequestFailed(request -> {
            String failure = request.failure();
            String url = request.url();
            logger.warn("网络请求失败: {} - {}", url, failure);
            
            // 如果是关键请求失败，也标记需要刷新
            if (url.contains("apple.com") && (failure.contains("timeout") || failure.contains("connection"))) {
                logger.warn("关键请求失败，标记需要刷新");
                http514ErrorDetected = true;
            }
        });
        
        logger.info("控制台和网络监听器已设置");
    }

    /**
     * 更新用户会话文件，保存地区选择信息
     * 在多线程模式下，将当前的浏览器状态（包括地区信息）保存到用户的会话文件中
     */
    private void updateUserSessionWithLocation() {
        // 只在多线程模式下更新会话文件
        if (injectedPage != null && userConfig != null) {
            try {
                // 获取当前页面的浏览器上下文
                BrowserContext context = injectedPage.context();
                
                // 获取用户会话文件路径
                String username = userConfig.getUsername();
                Path userSessionFile = Paths.get("users", username, "apple-auth.json");
                
                // 保存更新后的会话状态
                saveSessionState(context, userSessionFile);
                logger.info("已更新用户 {} 的会话文件，包含地区选择信息", username);
                
            } catch (Exception e) {
                logger.warn("更新用户会话文件失败: {}", e.getMessage(), e);
            }
        } else {
            logger.debug("非多线程模式或缺少用户配置，跳过会话文件更新");
        }
    }

    /**
     * 保存浏览器会话状态到文件
     * 
     * @param context    浏览器上下文
     * @param storageFile 保存文件路径
     */
    private void saveSessionState(BrowserContext context, Path storageFile) {
        try {
            // 确保父目录存在
            if (storageFile.getParent() != null) {
                Files.createDirectories(storageFile.getParent());
            }
            
            // 保存会话状态
            context.storageState(new BrowserContext.StorageStateOptions().setPath(storageFile));
            logger.info("会话状态已保存到: {}", storageFile.toAbsolutePath());
            
        } catch (Exception e) {
            logger.warn("保存会话状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 验证保存的会话是否仍然有效
     * 通过检查关键cookies的过期时间来判断，避免不必要的页面跳转
     * 
     * @param context 浏览器上下文
     * @return 会话是否有效
     */
    private boolean isSessionValid(BrowserContext context) {
        try {
            // 获取当前的存储状态
            String storageState = context.storageState();
            long currentTime = Instant.now().getEpochSecond();
            
            // 检查关键的Apple认证cookies及其过期时间
            String[] criticalCookies = {"as_disa", "aasp", "as_ltn_cn"};
            
            for (String cookieName : criticalCookies) {
                if (storageState.contains("\"name\":\"" + cookieName + "\"")) {
                    // 提取该cookie的过期时间
                    long expireTime = extractCookieExpireTime(storageState, cookieName);
                    
                    if (expireTime > 0) {
                        if (currentTime < expireTime) {
                            logger.info("关键cookie {} 尚未过期，过期时间: {}, 当前时间: {}", 
                                      cookieName, 
                                      Instant.ofEpochSecond(expireTime), 
                                      Instant.ofEpochSecond(currentTime));
                            return true;
                        } else {
                            logger.info("关键cookie {} 已过期，过期时间: {}, 当前时间: {}", 
                                      cookieName, 
                                      Instant.ofEpochSecond(expireTime), 
                                      Instant.ofEpochSecond(currentTime));
                        }
                    }
                }
            }
            
            // 检查基础cookies（无过期时间或永久有效的）
            if (storageState.contains("\"name\":\"dslang\"") && 
                storageState.contains("\"name\":\"site\"")) {
                logger.info("检测到基础Apple会话cookies，但无关键认证cookie或已过期");
            }
            
            logger.info("未检测到有效的Apple认证会话");
            return false;
            
        } catch (Exception e) {
            logger.warn("会话验证失败: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * 从存储状态JSON中提取指定cookie的过期时间
     * 
     * @param storageState 存储状态JSON字符串
     * @param cookieName   cookie名称
     * @return 过期时间的Unix时间戳，如果未找到或解析失败返回0
     */
    private long extractCookieExpireTime(String storageState, String cookieName) {
        try {
            // 构建正则表达式来匹配cookie及其expires字段
            String pattern = "\"name\":\"" + Pattern.quote(cookieName) + "\"[^}]*\"expires\":([0-9.]+)";
            Pattern regex = Pattern.compile(pattern);
            Matcher matcher = regex.matcher(storageState);
            
            if (matcher.find()) {
                String expireStr = matcher.group(1);
                return (long) Double.parseDouble(expireStr);
            }
            
        } catch (Exception e) {
            logger.debug("解析cookie {} 过期时间失败: {}", cookieName, e.getMessage());
        }
        
        return 0;
    }

}

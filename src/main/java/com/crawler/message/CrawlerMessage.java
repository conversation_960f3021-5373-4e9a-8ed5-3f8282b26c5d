package com.crawler.message;

import com.crawler.config.AppleIphoneConfig;
import com.crawler.config.AppleUserConfig;
import com.crawler.config.AppleWatchConfig;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.UUID;

/**
 * 爬虫任务消息实体
 * 用于在消息队列中传递不同类型的任务指令
 */
public class CrawlerMessage {
    
    /**
     * 消息类型枚举
     */
    public enum MessageType {
        /** 登录并生成会话文件 */
        LOGIN_AND_GENERATE_SESSION,
        
        /** 执行完整购买流程 */
        EXECUTE_PURCHASE_FLOW,
        
        /** 更新会话Cookie */
        UPDATE_SESSION_COOKIE,
        
        /** 验证会话有效性 */
        VALIDATE_SESSION,
        
        /** 清理过期会话 */
        CLEAN_EXPIRED_SESSION,
        
        /** 停止工作线程 */
        STOP_WORKER,
        
        /** 健康检查 */
        HEALTH_CHECK,
        
        /** 检查iPhone17Pro库存 */
        CHECK_IPHONE17PRO_STOCK,

        /** 加入 iPhone 购物车 */
        ADD_IPHONE17PRO_TO_CART,

        /** 加入 Apple Watch 购物车 */
        ADD_APPLE_WATCH_TO_CART
    }
    
    /**
     * 目标类型枚举
     */
    public enum TargetType {
        /** 特定用户 */
        SPECIFIC_USER,
        
        /** 所有用户 */
        ALL_USERS,
        
        /** 系统级操作 */
        SYSTEM
    }
    
    /** 消息唯一标识 */
    private final String messageId;
    
    /** 消息类型 */
    private final MessageType messageType;
    
    /** 目标类型 */
    private final TargetType targetType;
    
    /** 目标用户名（当targetType为SPECIFIC_USER时使用） */
    private final String targetUsername;
    
    /** 用户配置 */
    private final AppleUserConfig userConfig;
    
    /** iPhone配置 */
    private final AppleIphoneConfig iphoneConfig;

    /** Apple Watch 配置 */
    private final AppleWatchConfig watchConfig;

    /** 额外参数 */
    private final Map<String, Object> parameters;
    
    /** 创建时间 */
    private final LocalDateTime createdTime;
    
    /** 优先级（数字越小优先级越高） */
    private final int priority;
    
    /** 重试次数 */
    private int retryCount = 0;
    
    /** 最大重试次数 */
    private final int maxRetryCount;

    private CrawlerMessage(Builder builder) {
        this.messageId = builder.messageId != null ? builder.messageId : UUID.randomUUID().toString();
        this.messageType = builder.messageType;
        this.targetType = builder.targetType;
        this.targetUsername = builder.targetUsername;
        this.userConfig = builder.userConfig;
        this.iphoneConfig = builder.iphoneConfig;
        this.watchConfig = builder.watchConfig;
        this.parameters = builder.parameters;
        this.createdTime = builder.createdTime != null ? builder.createdTime : LocalDateTime.now();
        this.priority = builder.priority;
        this.maxRetryCount = builder.maxRetryCount;
    }

    // Getters
    public String getMessageId() {
        return messageId;
    }

    public MessageType getMessageType() {
        return messageType;
    }

    public TargetType getTargetType() {
        return targetType;
    }

    public String getTargetUsername() {
        return targetUsername;
    }

    public AppleUserConfig getUserConfig() {
        return userConfig;
    }

    public AppleIphoneConfig getIphoneConfig() {
        return iphoneConfig;
    }

    public AppleWatchConfig getWatchConfig() {
        return watchConfig;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public LocalDateTime getCreatedTime() {
        return createdTime;
    }

    public int getPriority() {
        return priority;
    }

    public int getRetryCount() {
        return retryCount;
    }

    public int getMaxRetryCount() {
        return maxRetryCount;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 检查是否还可以重试
     */
    public boolean canRetry() {
        return retryCount < maxRetryCount;
    }

    /**
     * 获取参数值
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        if (parameters == null) {
            return null;
        }
        Object value = parameters.get(key);
        if (value != null && type.isInstance(value)) {
            return (T) value;
        }
        return null;
    }

    /**
     * 获取参数值（带默认值）
     */
    public <T> T getParameter(String key, Class<T> type, T defaultValue) {
        T value = getParameter(key, type);
        return value != null ? value : defaultValue;
    }

    @Override
    public String toString() {
        return "CrawlerMessage{" +
                "messageId='" + messageId + '\'' +
                ", messageType=" + messageType +
                ", targetType=" + targetType +
                ", targetUsername='" + targetUsername + '\'' +
                ", priority=" + priority +
                ", retryCount=" + retryCount +
                ", maxRetryCount=" + maxRetryCount +
                ", createdTime=" + createdTime +
                '}';
    }

    /**
     * Builder 模式构造器
     */
    public static class Builder {
        private String messageId;
        private MessageType messageType;
        private TargetType targetType;
        private String targetUsername;
        private AppleUserConfig userConfig;
        private AppleIphoneConfig iphoneConfig;
        private AppleWatchConfig watchConfig;
        private Map<String, Object> parameters;
        private LocalDateTime createdTime;
        private int priority = 5; // 默认优先级
        private int maxRetryCount = 3; // 默认重试次数

        public Builder messageType(MessageType messageType) {
            this.messageType = messageType;
            return this;
        }

        public Builder targetSpecificUser(String username) {
            this.targetType = TargetType.SPECIFIC_USER;
            this.targetUsername = username;
            return this;
        }

        public Builder targetAllUsers() {
            this.targetType = TargetType.ALL_USERS;
            return this;
        }

        public Builder targetSystem() {
            this.targetType = TargetType.SYSTEM;
            return this;
        }

        public Builder userConfig(AppleUserConfig userConfig) {
            this.userConfig = userConfig;
            return this;
        }

        public Builder iphoneConfig(AppleIphoneConfig iphoneConfig) {
            this.iphoneConfig = iphoneConfig;
            return this;
        }

        public Builder watchConfig(AppleWatchConfig watchConfig) {
            this.watchConfig = watchConfig;
            return this;
        }

        public Builder parameters(Map<String, Object> parameters) {
            this.parameters = parameters;
            return this;
        }

        public Builder priority(int priority) {
            this.priority = priority;
            return this;
        }

        public Builder maxRetryCount(int maxRetryCount) {
            this.maxRetryCount = maxRetryCount;
            return this;
        }

        public Builder messageId(String messageId) {
            this.messageId = messageId;
            return this;
        }

        public Builder createdTime(LocalDateTime createdTime) {
            this.createdTime = createdTime;
            return this;
        }

        public CrawlerMessage build() {
            if (messageType == null) {
                throw new IllegalArgumentException("消息类型不能为空");
            }
            if (targetType == null) {
                throw new IllegalArgumentException("目标类型不能为空");
            }
            if (targetType == TargetType.SPECIFIC_USER && (targetUsername == null || targetUsername.trim().isEmpty())) {
                throw new IllegalArgumentException("当目标类型为特定用户时，用户名不能为空");
            }
            return new CrawlerMessage(this);
        }
    }

    /**
     * 创建登录会话消息的便捷方法
     */
    public static CrawlerMessage createLoginSessionMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.LOGIN_AND_GENERATE_SESSION)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .priority(1) // 登录操作高优先级
                .build();
    }

    /**
     * 创建购买流程消息的便捷方法
     */
    public static CrawlerMessage createPurchaseFlowMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.EXECUTE_PURCHASE_FLOW)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .priority(3) // 正常优先级
                .build();
    }

    /**
     * 创建更新会话Cookie消息的便捷方法
     */
    public static CrawlerMessage createUpdateSessionMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.UPDATE_SESSION_COOKIE)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .priority(2) // 较高优先级
                .build();
    }

    /**
     * 创建停止工作线程消息的便捷方法
     */
    public static CrawlerMessage createStopWorkerMessage() {
        return new Builder()
                .messageType(MessageType.STOP_WORKER)
                .targetSystem()
                .priority(0) // 最高优先级
                .build();
    }

    /**
     * 创建检查iPhone17Pro库存消息的便捷方法
     */
    public static CrawlerMessage createCheckStockMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.CHECK_IPHONE17PRO_STOCK)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .watchConfig(null)
                .priority(4) // 中等优先级
                .build();
    }

    /**
     * 创建加入购物车消息的便捷方法
     */
    public static CrawlerMessage createAddToCartMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.ADD_IPHONE17PRO_TO_CART)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .watchConfig(null)
                .priority(3) // 与购买流程类似的优先级
                .build();
    }

    /**
     * 创建加入 Apple Watch 购物车消息的便捷方法
     */
    public static CrawlerMessage createAddWatchToCartMessage(String username, AppleUserConfig userConfig,
            AppleIphoneConfig iphoneConfig, AppleWatchConfig watchConfig) {
        return new Builder()
                .messageType(MessageType.ADD_APPLE_WATCH_TO_CART)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .watchConfig(watchConfig)
                .priority(3)
                .build();
    }

    /**
     * 创建登录生成会话消息的便捷方法
     */
    public static CrawlerMessage createLoginMessage(String username, AppleUserConfig userConfig, AppleIphoneConfig iphoneConfig) {
        return new Builder()
                .messageType(MessageType.LOGIN_AND_GENERATE_SESSION)
                .targetSpecificUser(username)
                .userConfig(userConfig)
                .iphoneConfig(iphoneConfig)
                .priority(2) // 较高优先级
                .build();
    }
}

package com.crawler.worker;

import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.crawler.apple.AppleBusinessFlow;
import com.crawler.config.AppleWatchConfig;
import com.crawler.message.CrawlerMessage;
import com.crawler.user.UserProfileManager;
import com.microsoft.playwright.Browser;
import com.microsoft.playwright.BrowserContext;
import com.microsoft.playwright.BrowserType;
import com.microsoft.playwright.Page;
import com.microsoft.playwright.Playwright;
import com.microsoft.playwright.options.ColorScheme;
import com.microsoft.playwright.options.Geolocation;

/**
 * 用户专属爬虫工作线程
 * 每个线程绑定一个用户，包含持久的浏览器实例，处理该用户的所有任务
 */
public class CrawlerWorker implements Runnable {

    private static final Logger logger = LoggerFactory.getLogger(CrawlerWorker.class);

    /** 用户名 */
    private final String username;

    /** 消息队列 */
    private final BlockingQueue<CrawlerMessage> messageQueue;

    /** 用户配置管理器 */
    private final UserProfileManager userProfileManager;

    /** 是否无头模式 */
    private final boolean headless;

    // 持久的浏览器实例
    private Playwright playwright;
    private Browser browser;
    private volatile boolean browserInitialized = false;

    /** 停止标志 */
    private final AtomicBoolean shouldStop = new AtomicBoolean(false);

    /** 线程状态 */
    private volatile WorkerStatus status = WorkerStatus.STOPPED;

    /** 最后处理的消息时间 */
    private volatile LocalDateTime lastProcessTime;

    /** 处理的消息总数 */
    private volatile long processedMessageCount = 0;

    /** 处理成功的消息数 */
    private volatile long successMessageCount = 0;

    /** 处理失败的消息数 */
    private volatile long failedMessageCount = 0;

    /** 队列轮询超时时间（秒） */
    private static final long POLL_TIMEOUT_SECONDS = 5;

    public CrawlerWorker(String username, BlockingQueue<CrawlerMessage> messageQueue,
            UserProfileManager userProfileManager, boolean headless) {
        this.username = username;
        this.messageQueue = messageQueue;
        this.userProfileManager = userProfileManager;
        this.headless = false;

        // 初始化浏览器实例
        initializeBrowser();
    }

    /**
     * 初始化持久的浏览器实例
     */
    private synchronized void initializeBrowser() {
        if (browserInitialized) {
            return;
        }

        try {
            logger.info("用户 {} 初始化浏览器实例...", username);

            // 创建 Playwright 实例
            playwright = Playwright.create();

            // 使用普通的浏览器启动模式，带反检测参数
            BrowserType.LaunchOptions launchOptions = new BrowserType.LaunchOptions()
                    .setHeadless(headless)
                    .setArgs(Arrays.asList(
                            "--disable-blink-features=AutomationControlled",
                            "--start-maximized",
                            "--disable-webrtc"))
                    .setTimeout(120000);

            browser = playwright.chromium().launch(launchOptions);

            browserInitialized = true;
            logger.info("用户 {} 浏览器实例初始化完成", username);

        } catch (Exception e) {
            logger.error("用户 {} 初始化浏览器实例失败: {}", username, e.getMessage(), e);
            browserInitialized = false;
        }
    }

    @Override
    public void run() {
        logger.info("用户线程 {} 启动", username);
        status = WorkerStatus.RUNNING;

        try {
            while (!shouldStop.get() && !Thread.currentThread().isInterrupted()) {
                try {
                    // 从队列中获取消息，带超时以便能响应停止信号
                    CrawlerMessage message = messageQueue.poll(POLL_TIMEOUT_SECONDS, TimeUnit.SECONDS);

                    if (message == null) {
                        // 没有消息，继续轮询
                        status = WorkerStatus.WAITING;
                        continue;
                    }

                    // 只处理属于当前用户的消息
                    if (!username.equals(message.getTargetUsername()) &&
                            message.getTargetType() != CrawlerMessage.TargetType.ALL_USERS &&
                            message.getTargetType() != CrawlerMessage.TargetType.SYSTEM) {
                        // 不是当前用户的消息，重新放回队列
                        messageQueue.offer(message);
                        continue;
                    }

                    // 检查是否是停止消息
                    if (message.getMessageType() == CrawlerMessage.MessageType.STOP_WORKER) {
                        logger.info("用户线程 {} 收到停止信号", username);
                        break;
                    }

                    status = WorkerStatus.PROCESSING;
                    lastProcessTime = LocalDateTime.now();
                    processedMessageCount++;

                    logger.info("用户 {} 开始处理消息: {}", username, message.getMessageType());

                    // 直接处理消息
                    boolean success = processMessage(message);

                    // 统计处理结果
                    if (success) {
                        successMessageCount++;
                    } else {
                        failedMessageCount++;

                        // 如果需要重试且还可以重试，则重新放回队列
                        if (message.canRetry()) {
                            message.incrementRetryCount();
                            messageQueue.offer(message);
                            logger.warn("用户 {} 消息需要重试，已重新放入队列（重试次数: {}/{}）",
                                    username, message.getRetryCount(), message.getMaxRetryCount());
                        }
                    }

                } catch (InterruptedException e) {
                    logger.info("用户线程 {} 被中断", username);
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("用户线程 {} 处理消息时发生未预期异常: {}", username, e.getMessage(), e);
                    // 继续运行，不因为单个异常而停止整个工作线程
                }
            }
        } finally {
            status = WorkerStatus.STOPPED;

            // 关闭浏览器实例
            closeBrowser();

            logger.info("用户线程 {} 已停止，处理统计 - 总计: {}, 成功: {}, 失败: {}",
                    username, processedMessageCount, successMessageCount, failedMessageCount);
        }
    }

    /**
     * 处理单个消息
     */
    private boolean processMessage(CrawlerMessage message) {
        try {
            switch (message.getMessageType()) {
                case LOGIN_AND_GENERATE_SESSION:
                    return processLogin(message);

                case EXECUTE_PURCHASE_FLOW:
                    return processPurchase(message);

                case UPDATE_SESSION_COOKIE:
                    return processLogin(message); // 重新登录就是更新会话

                case ADD_IPHONE17PRO_TO_CART:
                    return processAddToCart(message);

                case ADD_APPLE_WATCH_TO_CART:
                    return processAddWatchToCart(message);

                case CHECK_IPHONE17PRO_STOCK:
                    return processStockCheck(message);

                case VALIDATE_SESSION:
                    return processValidateSession(message);

                case HEALTH_CHECK:
                    logger.info("用户 {} 健康检查通过", username);
                    return true;

                default:
                    logger.warn("用户 {} 不支持的消息类型: {}", username, message.getMessageType());
                    return false;
            }
        } catch (Exception e) {
            logger.error("用户 {} 处理消息时发生异常: {}", username, e.getMessage(), e);
            return false;
        }
    }

    /**
     * 处理登录任务
     */
    private boolean processLogin(CrawlerMessage message) {
        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始登录生成会话", username);

            context = createContextWithoutSession();
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig());
            businessFlow.loginAndGenerateSession();

            logger.info("用户 {} 登录会话生成成功", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 登录失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理购买任务
     */
    private boolean processPurchase(CrawlerMessage message) {
        // 检查会话文件是否存在
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法执行购买", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行购买流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig());
            businessFlow.executePurchaseFlow();

            logger.info("用户 {} 购买流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 购买流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理加入购物车任务
     */
    private boolean processAddToCart(CrawlerMessage message) {
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法加入购物车", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行加入购物车流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig());
            businessFlow.addIphone17ProToCart();

            logger.info("用户 {} 加入购物车流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} 加入购物车流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理 Apple Watch 加入购物车任务
     */
    private boolean processAddWatchToCart(CrawlerMessage message) {
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法加入手表购物车", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始执行 Apple Watch 加入购物车流程", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig());
            AppleWatchConfig watchConfig = message.getWatchConfig();
            if (watchConfig == null) {
                logger.error("用户 {} 未配置 Apple Watch 购买信息，无法执行加购", username);
                return false;
            }

            businessFlow.addAppleWatchUltraToCart(watchConfig);

            logger.info("用户 {} Apple Watch 加入购物车流程执行完成", username);
            return true;

        } catch (Exception e) {
            logger.error("用户 {} Apple Watch 加入购物车流程失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理库存检查任务
     */
    private boolean processStockCheck(CrawlerMessage message) {
        // 检查会话文件是否存在
        if (!userProfileManager.userSessionExists(username)) {
            logger.error("用户 {} 缺少会话文件，无法检查库存", username);
            return false;
        }

        BrowserContext context = null;

        try {
            logger.info("用户 {} 开始检查iPhone17Pro库存", username);

            Path sessionPath = userProfileManager.getSessionFilePath(username);
            context = createContextWithSession(sessionPath);
            Page page = context.newPage();

            AppleBusinessFlow businessFlow = new AppleBusinessFlow(page, message.getUserConfig(),
                    message.getIphoneConfig(), message.getPickupLocationConfig());
            boolean hasStock = businessFlow.checkIphone17ProStock();

            if (hasStock) {
                logger.warn("🎉 重要！用户 {} 的iPhone17Pro有库存了！", username);
            } else {
                logger.info("用户 {} iPhone17Pro暂时无库存", username);
            }

            return true;

        } catch (Exception e) {
            logger.error("用户 {} 库存检查失败: {}", username, e.getMessage(), e);
            return false;
        } finally {
            if (context != null) {
                try {
                    context.close();
                } catch (Exception e) {
                    logger.warn("关闭上下文时出错: {}", e.getMessage());
                }
            }
        }
    }

    /**
     * 处理会话验证任务
     */
    private boolean processValidateSession(CrawlerMessage message) {
        return userProfileManager.userSessionExists(username);
    }

    /**
     * 创建带会话的浏览器上下文
     */
    private BrowserContext createContextWithSession(Path sessionPath) {
        ensureBrowserAvailable();

        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setStorageStatePath(sessionPath)
                .setViewportSize(null);

        // 应用真实环境参数
        String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                + "AppleWebKit/537.36 (KHTML, like Gecko) "
                + "Chrome/********* Safari/537.36";
        contextOptions
                .setUserAgent(realUserAgent)
                .setLocale("zh-CN")
                .setTimezoneId("Asia/Shanghai")
                .setColorScheme(ColorScheme.LIGHT)
                .setExtraHTTPHeaders(Map.of(
                        "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                        "Sec-CH-UA-Platform", "\"macOS\""))
                .setGeolocation(new Geolocation(31.2304, 121.4737));

        BrowserContext context = browser.newContext(contextOptions);

        // 添加反检测脚本
        context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
        context.grantPermissions(Arrays.asList("geolocation", "notifications"));

        return context;
    }

    /**
     * 创建无会话的浏览器上下文
     */
    private BrowserContext createContextWithoutSession() {
        ensureBrowserAvailable();

        Browser.NewContextOptions contextOptions = new Browser.NewContextOptions()
                .setViewportSize(null);

        // 应用真实环境参数
        String realUserAgent = "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) "
                + "AppleWebKit/537.36 (KHTML, like Gecko) "
                + "Chrome/********* Safari/537.36";
        contextOptions
                .setUserAgent(realUserAgent)
                .setLocale("zh-CN")
                .setTimezoneId("Asia/Shanghai")
                .setColorScheme(ColorScheme.LIGHT)
                .setExtraHTTPHeaders(Map.of(
                        "Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8",
                        "Sec-CH-UA-Platform", "\"macOS\""))
                .setGeolocation(new Geolocation(31.2304, 121.4737));

        BrowserContext context = browser.newContext(contextOptions);

        // 添加反检测脚本
        context.addInitScript("Object.defineProperty(navigator, 'webdriver', { get: () => false });");
        context.grantPermissions(Arrays.asList("geolocation", "notifications"));

        return context;
    }

    /**
     * 确保浏览器实例可用
     */
    private void ensureBrowserAvailable() {
        if (!browserInitialized || playwright == null || browser == null) {
            logger.warn("用户 {} 浏览器实例不可用，重新初始化", username);
            closeBrowser();
            initializeBrowser();
        }
    }

    /**
     * 优雅停止工作线程
     */
    public void shutdown() {
        logger.info("请求停止用户线程 {}", username);
        shouldStop.set(true);
    }

    /**
     * 强制停止工作线程
     */
    public void forceShutdown() {
        logger.warn("强制停止用户线程 {}", username);
        shouldStop.set(true);

        // 关闭浏览器实例
        closeBrowser();

        Thread.currentThread().interrupt();
    }

    /**
     * 关闭持久浏览器实例
     */
    private synchronized void closeBrowser() {
        logger.info("用户 {} 关闭浏览器实例...", username);

        if (browser != null) {
            try {
                browser.close();
            } catch (Exception e) {
                logger.warn("关闭浏览器时出错: {}", e.getMessage());
            }
            browser = null;
        }

        if (playwright != null) {
            try {
                playwright.close();
            } catch (Exception e) {
                logger.warn("关闭 Playwright 时出错: {}", e.getMessage());
            }
            playwright = null;
        }

        browserInitialized = false;
        logger.info("用户 {} 浏览器实例已关闭", username);
    }

    // Getters
    public String getUsername() {
        return username;
    }

    public String getWorkerId() {
        return username; // 现在工作线程ID就是用户名
    }

    public WorkerStatus getStatus() {
        return status;
    }

    public LocalDateTime getLastProcessTime() {
        return lastProcessTime;
    }

    public long getProcessedMessageCount() {
        return processedMessageCount;
    }

    public long getSuccessMessageCount() {
        return successMessageCount;
    }

    public long getFailedMessageCount() {
        return failedMessageCount;
    }

    public int getQueueSize() {
        return messageQueue.size();
    }

    /**
     * 获取工作线程统计信息
     */
    public WorkerStats getStats() {
        return new WorkerStats(
                username,
                status,
                processedMessageCount,
                successMessageCount,
                failedMessageCount,
                lastProcessTime,
                messageQueue.size());
    }

    @Override
    public String toString() {
        return "CrawlerWorker{" +
                "username='" + username + '\'' +
                ", status=" + status +
                ", processedMessages=" + processedMessageCount +
                ", queueSize=" + messageQueue.size() +
                '}';
    }

    /**
     * 工作线程状态枚举
     */
    public enum WorkerStatus {
        /** 已停止 */
        STOPPED,
        /** 运行中 */
        RUNNING,
        /** 等待消息 */
        WAITING,
        /** 处理消息中 */
        PROCESSING
    }

    /**
     * 工作线程统计信息
     */
    public static class WorkerStats {
        private final String workerId;
        private final WorkerStatus status;
        private final long processedMessageCount;
        private final long successMessageCount;
        private final long failedMessageCount;
        private final LocalDateTime lastProcessTime;
        private final int queueSize;

        public WorkerStats(String workerId, WorkerStatus status, long processedMessageCount,
                long successMessageCount, long failedMessageCount,
                LocalDateTime lastProcessTime, int queueSize) {
            this.workerId = workerId;
            this.status = status;
            this.processedMessageCount = processedMessageCount;
            this.successMessageCount = successMessageCount;
            this.failedMessageCount = failedMessageCount;
            this.lastProcessTime = lastProcessTime;
            this.queueSize = queueSize;
        }

        // Getters
        public String getWorkerId() {
            return workerId;
        }

        public WorkerStatus getStatus() {
            return status;
        }

        public long getProcessedMessageCount() {
            return processedMessageCount;
        }

        public long getSuccessMessageCount() {
            return successMessageCount;
        }

        public long getFailedMessageCount() {
            return failedMessageCount;
        }

        public LocalDateTime getLastProcessTime() {
            return lastProcessTime;
        }

        public int getQueueSize() {
            return queueSize;
        }

        @Override
        public String toString() {
            return "WorkerStats{" +
                    "workerId='" + workerId + '\'' +
                    ", status=" + status +
                    ", processed=" + processedMessageCount +
                    ", success=" + successMessageCount +
                    ", failed=" + failedMessageCount +
                    ", queueSize=" + queueSize +
                    ", lastProcess=" + lastProcessTime +
                    '}';
        }
    }
}

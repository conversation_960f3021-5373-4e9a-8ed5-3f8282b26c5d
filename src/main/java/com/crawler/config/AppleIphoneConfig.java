package com.crawler.config;

import java.util.ArrayList;
import java.util.List;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * iPhone购买配置类
 * 用于管理要购买的iPhone型号、颜色、容量等信息
 */
@Component
@ConfigurationProperties(prefix = "apple.iphone")
public class AppleIphoneConfig {
    
    // 购买页面URL
    private String buyUrl;
    private String checkoutUrl;
    
    // iPhone型号信息
    private String model;
    private String modelDisplayName;
    private boolean requiresModelSelection = true; // 默认需要手动选择型号
    private List<ColorOption> alternateColors = new ArrayList<>();
    
    // 颜色信息
    private String color;
    private String colorDisplayName;
    
    // 存储容量信息
    private String storage;
    private String storageDisplayName;
    
    // 其他选项
    private boolean tradeIn = false;  // 是否折抵换购
    private boolean appleCare = false; // 是否加AppleCare+
    
    // 默认构造函数
    public AppleIphoneConfig() {}
    
    // Getters and Setters
    public String getBuyUrl() {
        return buyUrl;
    }
    
    public void setBuyUrl(String buyUrl) {
        this.buyUrl = buyUrl;
    }

    public String getCheckoutUrl() {
        return checkoutUrl;
    }

    public void setCheckoutUrl(String checkoutUrl) {
        this.checkoutUrl = checkoutUrl;
    }

    // 保持旧命名兼容（CheckOut -> Checkout）
    public String getCheckOutUrl() {
        return getCheckoutUrl();
    }

    public void setCheckOutUrl(String checkoutUrl) {
        setCheckoutUrl(checkoutUrl);
    }

    public String getModel() {
        return model;
    }
    
    public void setModel(String model) {
        this.model = model;
    }
    
    public String getModelDisplayName() {
        return modelDisplayName;
    }

    public void setModelDisplayName(String modelDisplayName) {
        this.modelDisplayName = modelDisplayName;
    }

    public boolean isRequiresModelSelection() {
        return requiresModelSelection;
    }

    public void setRequiresModelSelection(boolean requiresModelSelection) {
        this.requiresModelSelection = requiresModelSelection;
    }

    public List<ColorOption> getAlternateColors() {
        return alternateColors;
    }

    public void setAlternateColors(List<ColorOption> alternateColors) {
        this.alternateColors = alternateColors != null ? alternateColors : new ArrayList<>();
    }
    
    public String getColor() {
        return color;
    }
    
    public void setColor(String color) {
        this.color = color;
    }
    
    public String getColorDisplayName() {
        return colorDisplayName;
    }
    
    public void setColorDisplayName(String colorDisplayName) {
        this.colorDisplayName = colorDisplayName;
    }
    
    public String getStorage() {
        return storage;
    }
    
    public void setStorage(String storage) {
        this.storage = storage;
    }
    
    public String getStorageDisplayName() {
        return storageDisplayName;
    }
    
    public void setStorageDisplayName(String storageDisplayName) {
        this.storageDisplayName = storageDisplayName;
    }
    
    public boolean isTradeIn() {
        return tradeIn;
    }
    
    public void setTradeIn(boolean tradeIn) {
        this.tradeIn = tradeIn;
    }
    
    public boolean isAppleCare() {
        return appleCare;
    }
    
    public void setAppleCare(boolean appleCare) {
        this.appleCare = appleCare;
    }
    
    @Override
    public String toString() {
        return "AppleIphoneConfig{" +
                "buyUrl='" + buyUrl + '\'' +
                ", checkoutUrl='" + checkoutUrl + '\'' +
                ", model='" + model + '\'' +
                ", modelDisplayName='" + modelDisplayName + '\'' +
                ", requiresModelSelection=" + requiresModelSelection +
                ", alternateColors=" + alternateColors +
                ", color='" + color + '\'' +
                ", colorDisplayName='" + colorDisplayName + '\'' +
                ", storage='" + storage + '\'' +
                ", storageDisplayName='" + storageDisplayName + '\'' +
                ", tradeIn=" + tradeIn +
                ", appleCare=" + appleCare +
                '}';
    }

    /**
     * 颜色选项配置
     */
    public static class ColorOption {
        private String color;
        private String colorDisplayName;

        public String getColor() {
            return color;
        }

        public void setColor(String color) {
            this.color = color;
        }

        public String getColorDisplayName() {
            return colorDisplayName;
        }

        public void setColorDisplayName(String colorDisplayName) {
            this.colorDisplayName = colorDisplayName;
        }

        @Override
        public String toString() {
            return "ColorOption{" +
                    "color='" + color + '\'' +
                    ", colorDisplayName='" + colorDisplayName + '\'' +
                    '}';
        }
    }
}

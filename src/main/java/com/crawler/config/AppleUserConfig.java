package com.crawler.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * Apple爬虫用户信息配置类
 * 用于管理登录信息和填写信息
 */
@Component
@ConfigurationProperties(prefix = "apple.user")
public class AppleUserConfig {
    
    // 登录信息
    private String username;
    private String password;
    
    // 个人信息
    private String firstName;
    private String lastName;
    private String phone;
    private String email;
    
    // 地址信息
    private String address;
    private String city;
    private String province;
    private String postalCode;
    
    // 身份证信息
    private String idCardNumber;
    private String idCardName;
    
    // 默认构造函数
    public AppleUserConfig() {}
    
    // Getters and Setters
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getPassword() {
        return password;
    }
    
    public void setPassword(String password) {
        this.password = password;
    }
    
    public String getFirstName() {
        return firstName;
    }
    
    public void setFirstName(String firstName) {
        this.firstName = firstName;
    }
    
    public String getLastName() {
        return lastName;
    }
    
    public void setLastName(String lastName) {
        this.lastName = lastName;
    }
    
    public String getPhone() {
        return phone;
    }
    
    public void setPhone(String phone) {
        this.phone = phone;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public String getCity() {
        return city;
    }
    
    public void setCity(String city) {
        this.city = city;
    }
    
    public String getProvince() {
        return province;
    }
    
    public void setProvince(String province) {
        this.province = province;
    }
    
    public String getPostalCode() {
        return postalCode;
    }
    
    public void setPostalCode(String postalCode) {
        this.postalCode = postalCode;
    }
    
    public String getIdCardNumber() {
        return idCardNumber;
    }
    
    public void setIdCardNumber(String idCardNumber) {
        this.idCardNumber = idCardNumber;
    }
    
    public String getIdCardName() {
        return idCardName;
    }
    
    public void setIdCardName(String idCardName) {
        this.idCardName = idCardName;
    }
    
    @Override
    public String toString() {
        return "AppleUserConfig{" +
                "username='" + username + '\'' +
                ", password='[PROTECTED]'" +
                ", firstName='" + firstName + '\'' +
                ", lastName='" + lastName + '\'' +
                ", phone='" + phone + '\'' +
                ", email='" + email + '\'' +
                ", address='" + address + '\'' +
                ", city='" + city + '\'' +
                ", province='" + province + '\'' +
                ", postalCode='" + postalCode + '\'' +
                ", idCardNumber='[PROTECTED]'" +
                ", idCardName='" + idCardName + '\'' +
                '}';
    }
}

package com.crawler.service;

import com.crawler.message.CrawlerMessage;
import com.crawler.queue.MessageQueueManager;
import com.crawler.user.UserProfileManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息队列服务
 * 管理消息队列和定时任务
 */
@Service
public class MessageQueueService {
    
    private static final Logger logger = LoggerFactory.getLogger(MessageQueueService.class);
    
    @Autowired
    private UserProfileManager userProfileManager;
    
    @Value("${apple.crawler.thread.count:5}")
    private int workerCount;
    
    @Value("${apple.login.headless:true}")
    private boolean headless;
    
    @Value("${apple.session.refresh.interval.hours:24}")
    private int sessionRefreshIntervalHours;
    
    @Value("${apple.health.check.interval.minutes:30}")
    private int healthCheckIntervalMinutes;
    
    private MessageQueueManager queueManager;
    private ScheduledExecutorService scheduledExecutorService;

    @PostConstruct
    public void initialize() {
        logger.info("初始化消息队列服务...");
        
        // 初始化用户配置管理器
        userProfileManager.initialize();
        
        // 创建消息队列管理器
        queueManager = new MessageQueueManager(workerCount, headless, userProfileManager);
        
        // 创建定时任务执行器
        scheduledExecutorService = Executors.newScheduledThreadPool(2, new ThreadFactory() {
            private final AtomicLong threadCounter = new AtomicLong(0);
            
            @Override
            public Thread newThread(Runnable r) {
                Thread thread = new Thread(r);
                thread.setName("ScheduledTask-" + threadCounter.incrementAndGet());
                thread.setDaemon(true);
                return thread;
            }
        });
        
        logger.info("消息队列服务初始化完成");
    }

    /**
     * 启动消息队列服务
     */
    public void start() {
        if (queueManager == null) {
            throw new IllegalStateException("消息队列服务尚未初始化");
        }
        
        logger.info("启动消息队列服务...");
        
        // 启动队列管理器
        queueManager.start();
        
        // 启动定时任务
        startScheduledTasks();
        
        logger.info("消息队列服务启动完成");
    }

    /**
     * 启动定时任务
     */
    private void startScheduledTasks() {
        // 定时会话刷新任务
        if (sessionRefreshIntervalHours > 0) {
            scheduledExecutorService.scheduleAtFixedRate(
                    this::scheduleSessionRefresh,
                    sessionRefreshIntervalHours, 
                    sessionRefreshIntervalHours,
                    TimeUnit.HOURS
            );
            logger.info("定时会话刷新任务已启动，间隔: {} 小时", sessionRefreshIntervalHours);
        }
        
        // 定时健康检查任务
        if (healthCheckIntervalMinutes > 0) {
            scheduledExecutorService.scheduleAtFixedRate(
                    this::scheduleHealthCheck,
                    healthCheckIntervalMinutes,
                    healthCheckIntervalMinutes,
                    TimeUnit.MINUTES
            );
            logger.info("定时健康检查任务已启动，间隔: {} 分钟", healthCheckIntervalMinutes);
        }
    }

    /**
     * 定时会话刷新任务
     */
    private void scheduleSessionRefresh() {
        try {
            logger.info("执行定时会话刷新任务...");
            int count = queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.UPDATE_SESSION_COOKIE, 2);
            logger.info("定时会话刷新任务完成，发送消息数: {}", count);
        } catch (Exception e) {
            logger.error("定时会话刷新任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 定时健康检查任务
     */
    private void scheduleHealthCheck() {
        try {
            logger.debug("执行定时健康检查任务...");
            int count = queueManager.sendHealthCheckToAllWorkers();
            logger.debug("定时健康检查任务完成，发送消息数: {}", count);
        } catch (Exception e) {
            logger.error("定时健康检查任务失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 向特定用户发送登录消息
     */
    public boolean sendLoginMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.LOGIN_AND_GENERATE_SESSION, 1);
    }

    /**
     * 向特定用户发送购买流程消息
     */
    public boolean sendPurchaseMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.EXECUTE_PURCHASE_FLOW, 3);
    }

    /**
     * 向特定用户发送加入购物车消息
     */
    public boolean sendAddToCartMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.ADD_IPHONE17PRO_TO_CART, 3);
    }

    /**
     * 向特定用户发送手表加入购物车消息
     */
    public boolean sendAddWatchToCartMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.ADD_APPLE_WATCH_TO_CART, 3);
    }

    /**
     * 向所有用户发送购买流程消息
     */
    public int sendPurchaseMessageToAllUsers() {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return 0;
        }
        return queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.EXECUTE_PURCHASE_FLOW, 3);
    }

    /**
     * 向所有用户发送加入购物车消息
     */
    public int sendAddToCartMessageToAllUsers() {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return 0;
        }
        return queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.ADD_IPHONE17PRO_TO_CART, 3);
    }

    /**
     * 向所有用户发送手表加入购物车消息
     */
    public int sendAddWatchToCartMessageToAllUsers() {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return 0;
        }
        return queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.ADD_APPLE_WATCH_TO_CART, 3);
    }

    /**
     * 向特定用户发送会话更新消息
     */
    public boolean sendSessionUpdateMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.UPDATE_SESSION_COOKIE, 2);
    }

    /**
     * 向所有用户发送会话更新消息
     */
    public int sendSessionUpdateMessageToAllUsers() {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return 0;
        }
        return queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.UPDATE_SESSION_COOKIE, 2);
    }

    /**
     * 向特定用户发送库存检查消息
     */
    public boolean sendStockCheckMessage(String username) {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return false;
        }
        return queueManager.sendMessageToUser(username, CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK, 4);
    }

    /**
     * 向所有用户发送库存检查消息
     */
    public int sendStockCheckMessageToAllUsers() {
        if (queueManager == null) {
            logger.error("消息队列服务尚未启动");
            return 0;
        }
        return queueManager.sendMessageToAllUsers(CrawlerMessage.MessageType.CHECK_IPHONE17PRO_STOCK, 4);
    }

    /**
     * 获取队列状态
     */
    public MessageQueueManager.QueueStatus getQueueStatus() {
        if (queueManager == null) {
            return null;
        }
        return queueManager.getQueueStatus();
    }

    /**
     * 打印队列状态
     */
    public void printQueueStatus() {
        if (queueManager == null) {
            System.out.println("❌ 消息队列服务尚未启动");
            return;
        }
        
        var status = queueManager.getQueueStatus();
        var workerStats = queueManager.getWorkerStats();
        
        System.out.println("📊 消息队列状态:");
        System.out.println("  队列长度: " + status.getQueueSize());
        System.out.println("  工作线程: " + status.getActiveWorkers() + "/" + status.getTotalWorkers());
        System.out.println("  已处理消息: " + status.getTotalProcessedMessages());
        System.out.println("  成功: " + status.getTotalSuccessMessages());
        System.out.println("  失败: " + status.getTotalFailedMessages());
        
        System.out.println("\n🔧 工作线程详情:");
        for (var stats : workerStats) {
            System.out.println("  " + stats.getWorkerId() + ": " + stats.getStatus() + 
                             " (处理: " + stats.getProcessedMessageCount() + 
                             ", 成功: " + stats.getSuccessMessageCount() + 
                             ", 失败: " + stats.getFailedMessageCount() + ")");
        }
    }

    /**
     * 停止消息队列服务
     */
    @PreDestroy
    public void shutdown() {
        logger.info("关闭消息队列服务...");
        
        // 关闭定时任务
        if (scheduledExecutorService != null) {
            scheduledExecutorService.shutdown();
            try {
                if (!scheduledExecutorService.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduledExecutorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduledExecutorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        // 关闭队列管理器
        if (queueManager != null) {
            queueManager.shutdown();
        }
        
        logger.info("消息队列服务已关闭");
    }

    public MessageQueueManager getQueueManager() {
        return queueManager;
    }
}

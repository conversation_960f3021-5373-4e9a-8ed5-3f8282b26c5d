spring.application.name=crawler

# Apple登录爬虫配置
apple.login.username=<EMAIL>
apple.login.password=Aa112211
apple.login.headless=false

# Apple iPhone购买配置
apple.iphone.buyUrl=https://www.apple.com.cn/shop/buy-iphone/iphone-16
apple.iphone.model=iphone-16
apple.iphone.modelDisplayName=iPhone 16
apple.iphone.color=black
apple.iphone.colorDisplayName=\u9ED1\u8272
apple.iphone.storage=128gb
apple.iphone.storageDisplayName=128GB \u811A\u6CE8
apple.iphone.tradeIn=false
apple.iphone.appleCare=false

# Apple用户信息配置
apple.user.username=<EMAIL>
apple.user.password=Aa112211
apple.user.firstName=\u6D9B
apple.user.lastName=\u66F9
apple.user.phone=13888858880
apple.user.email=<EMAIL>
apple.user.address=\u5317\u4EAC\u5E02\u671D\u9633\u533A
apple.user.city=\u5317\u4EAC
apple.user.province=\u5317\u4EAC
apple.user.postalCode=100000
apple.user.idCardNumber=110101199001011234
apple.user.idCardName=\u66F9\u6D9B

# Apple文件路径配置
apple.auth.file.path=apple-auth.json
apple.screenshot.dir=./AppleScreenshots

# Apple视频录制配置
# headless模式下默认启用视频录制，方便调试问题
apple.record.video=true
apple.video.dir=videos

# Apple取货信息重试配置
# 如果昆明取货信息未出现，刷新页面重试的次数（默认: 3）
apple.pickup.retry.count=3

# Apple网络错误检测配置
# 启用HTTP 514等服务器错误的自动检测和快速重试（默认: true）
apple.network.error.detection=true

# Apple浏览器模式配置
# 使用持久化上下文模式（默认: false，使用普通模式）
# true: 持久化上下文模式，会话自动保存在 my-apple-session 目录
# false: 普通模式，会话保存在 apple-auth.json 文件
apple.use.persistent.context=false

# Apple多线程爬虫配置
# 并发线程数（默认: 5）
apple.crawler.thread.count=5

# 日志级别配置
logging.level.com.crawler.apple=DEBUG
logging.level.com.crawler.task=DEBUG
logging.level.com.crawler.session=DEBUG

# Web 服务默认配置
server.port=8080
# 可选：设置上下文路径（需要时开启）
# server.servlet.context-path=/

# 可选：简单CORS放开（如需跨域测试可开启）
# spring.mvc.cors.mappings[0].path-pattern=/api/**
# spring.mvc.cors.mappings[0].allowed-origins=*
# spring.mvc.cors.mappings[0].allowed-methods=GET,POST,PUT,DELETE,OPTIONS
